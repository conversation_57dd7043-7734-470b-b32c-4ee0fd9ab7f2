import streamlit as st
import pandas as pd
import numpy as np
from scipy import stats
import streamlit_highcharts as hct
import io
from datetime import datetime

# Page configuration
st.set_page_config(
    page_title="Sales and Deliveries Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-container {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .sidebar .sidebar-content {
        background-color: #f8f9fa;
    }
    /* Enhanced table styling for delivery ratios */
    .stDataFrame table td:nth-child(n):contains("Ratio") {
        font-weight: bold !important;
        background-color: #e8f4f8 !important;
        color: #1f77b4 !important;
    }
    .stDataFrame table th:nth-child(n):contains("Ratio") {
        font-weight: bold !important;
        background-color: #d1ecf1 !important;
        color: #0c5460 !important;
    }
    /* Week selector styling */
    .week-selector-container {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_data():
    """Load and cache the parquet data"""
    try:
        df = pd.read_parquet("24w26_25w26.parquet")
        return df
    except FileNotFoundError:
        st.error("Data file '24w26_25w26.parquet' not found. Please ensure the file is in the correct directory.")
        return None

def parse_custom_week(week_str):
    """Parse custom week format to numeric value for sorting"""
    if pd.isna(week_str):
        return 0
    year = int(week_str[1:5])
    week = int(week_str[6:8])
    return year * 100 + week

def format_week_label(week_str):
    """Format week string for display"""
    if pd.isna(week_str):
        return ""
    # Convert f2024w26 to 24w26
    year = week_str[3:5]  # Get last 2 digits of year
    week = week_str[6:8]  # Get week number
    return f"{year}w{week}"

def format_large_number(num):
    """Format large numbers with K/M suffixes"""
    if pd.isna(num):
        return "0"
    if num >= 1_000_000:
        return f"{num/1_000_000:.1f}M"
    elif num >= 1_000:
        return f"{num/1_000:.1f}K"
    else:
        return f"{num:.0f}"

def create_advanced_week_selector(df):
    """Create advanced week selection component at top of sidebar"""

    st.sidebar.markdown('<div class="week-selector-container">', unsafe_allow_html=True)
    st.sidebar.markdown("### 📅 Week Selection")
    st.sidebar.markdown("*Applies to both Country and Department Analysis*")

    # Get all available weeks
    all_weeks = sorted(df['week'].unique(), key=parse_custom_week)

    # Create formatted week options for display
    week_options = [format_week_label(week) for week in all_weeks]
    week_to_original = dict(zip(week_options, all_weeks))

    # Range selector with formatted labels
    col1, col2 = st.sidebar.columns(2)

    with col1:
        start_week = st.selectbox(
            "Start Week:",
            week_options,
            index=0,
            key="start_week_selector"
        )

    with col2:
        end_week = st.selectbox(
            "End Week:",
            week_options,
            index=len(week_options)-1,
            key="end_week_selector"
        )

    # Get range of weeks
    start_idx = week_options.index(start_week)
    end_idx = week_options.index(end_week)

    if start_idx <= end_idx:
        selected_weeks = [week_to_original[week_options[i]] for i in range(start_idx, end_idx + 1)]
    else:
        st.sidebar.error("Start week must be before or equal to end week")
        selected_weeks = all_weeks

    # Display selection summary
    if selected_weeks:
        st.sidebar.markdown(f"**Selected:** {len(selected_weeks)} weeks")
        st.sidebar.markdown(f"**Range:** {format_week_label(selected_weeks[0])} to {format_week_label(selected_weeks[-1])}")

    st.sidebar.markdown('</div>', unsafe_allow_html=True)
    st.sidebar.markdown("---")

    return selected_weeks if selected_weeks else all_weeks

def create_excel_download(table_data, is_pmg_level):
    """Create Excel file for download"""

    # Create Excel buffer
    excel_buffer = io.BytesIO()

    # Create Excel writer
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        # Write main data
        sheet_name = "PMG_Analysis" if is_pmg_level else "Department_Analysis"
        table_data.to_excel(writer, sheet_name=sheet_name, index=False)

        # Get worksheet to apply formatting
        worksheet = writer.sheets[sheet_name]

        # Apply formatting to ratio columns
        try:
            from openpyxl.styles import Font, PatternFill

            # Find ratio columns
            ratio_cols = []
            for idx, col in enumerate(table_data.columns, 1):
                if 'Ratio %' in col:
                    ratio_cols.append(idx)

            # Apply formatting to ratio columns
            blue_fill = PatternFill(start_color="E8F4F8", end_color="E8F4F8", fill_type="solid")
            bold_font = Font(bold=True, color="1F77B4")

            for col_idx in ratio_cols:
                for row_idx in range(2, len(table_data) + 2):  # Skip header row
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell.fill = blue_fill
                    cell.font = bold_font

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        except ImportError:
            # If openpyxl styling is not available, just save without formatting
            pass

    excel_buffer.seek(0)
    return excel_buffer.getvalue()

@st.cache_data
def create_recommendation_system(filtered_df, selected_weeks):
    """Create recommendation system to find optimal 12-week periods closest to 100% delivery ratio"""

    with st.spinner("🔍 Analyzing optimal 12-week periods across all countries..."):
        # Calculate weekly ratios for each department and country
        weekly_data = filtered_df.groupby(['dep name', 'country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()
        weekly_data['delivery_ratio'] = (weekly_data['unit_delivered'] / weekly_data['sold_units'] * 100).round(2)

        # Get all departments
        departments = sorted(weekly_data['dep name'].unique())

        recommendations = []

        for dept in departments:
            dept_data = weekly_data[weekly_data['dep name'] == dept].copy()

            # Find the optimal 12-week consecutive period for this department
            best_period = find_optimal_12week_period(dept_data, selected_weeks)

            if best_period:
                # Calculate distance from 100% for display
                distance_from_100 = abs(100 - best_period['overall_avg'])

                recommendations.append({
                    'Department': dept,
                    'Optimal Period Start': format_week_label(best_period['start_week']),
                    'Optimal Period End': format_week_label(best_period['end_week']),
                    'Period Length': '12 weeks',
                    'Average Ratio CZ': f"{best_period['avg_ratios']['CZ']:.1f}%",
                    'Average Ratio HU': f"{best_period['avg_ratios']['HU']:.1f}%",
                    'Average Ratio SK': f"{best_period['avg_ratios']['SK']:.1f}%",
                    'Overall Average': f"{best_period['overall_avg']:.1f}%",
                    'Distance from 100%': f"{distance_from_100:.1f}%",
                    'Optimization Score': f"{best_period['score']:.2f}"
                })

        if recommendations:
            recommendations_df = pd.DataFrame(recommendations)

            # Sort by distance from 100% (best optimization first)
            recommendations_df['_sort_distance'] = recommendations_df['Distance from 100%'].str.replace('%', '').astype(float)
            recommendations_df = recommendations_df.sort_values('_sort_distance').drop('_sort_distance', axis=1)

            # Apply styling to ratio columns
            def highlight_ratio_columns(df):
                styles = pd.DataFrame('', index=df.index, columns=df.columns)

                ratio_columns = ['Average Ratio CZ', 'Average Ratio HU', 'Average Ratio SK', 'Overall Average', 'Distance from 100%']
                for col in ratio_columns:
                    if col in df.columns:
                        styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'

                return styles

            styled_recommendations = recommendations_df.style.apply(highlight_ratio_columns, axis=None)
            st.dataframe(styled_recommendations, use_container_width=True, height=400)

            # Find department with ratio closest to 100%
            best_dept = min(recommendations, key=lambda x: float(x['Distance from 100%'].replace('%', '')))
            st.success(f"🎯 **Most Optimal Performance:** {best_dept['Department']} during {best_dept['Optimal Period Start']} to {best_dept['Optimal Period End']} with {best_dept['Overall Average']} average delivery ratio (only {best_dept['Distance from 100%']} away from perfect 100%)")

            # Show analysis summary
            st.info(f"📊 **Analysis Summary:** Found optimal 12-week periods for {len(recommendations)} departments. Periods are ranked by proximity to 100% delivery ratio across all countries (CZ, HU, SK).")

        else:
            st.warning("⚠️ No sufficient 12-week periods found for recommendation analysis. Try selecting a longer time range (at least 12 weeks).")

def find_optimal_12week_period(dept_data, selected_weeks):
    """Find the optimal 12-week consecutive period closest to 100% delivery ratio across all countries"""

    countries = ['CZ', 'HU', 'SK']
    required_period_length = 12  # Exactly 12 weeks required

    # Check if we have enough weeks for 12-week analysis
    if len(selected_weeks) < required_period_length:
        return None

    best_period = None
    best_score = float('inf')  # Lower score is better (closest to 100%)

    # Try all possible 12-week consecutive periods
    for start_idx in range(len(selected_weeks) - required_period_length + 1):
        end_idx = start_idx + required_period_length - 1
        period_weeks = selected_weeks[start_idx:end_idx + 1]

        # Validate that we have exactly 12 weeks
        if len(period_weeks) != required_period_length:
            continue

        # Calculate average ratios for this 12-week period
        period_ratios = {}
        valid_countries = 0

        # Check data completeness for each country
        for country in countries:
            country_data = dept_data[
                (dept_data['country'] == country) &
                (dept_data['week'].isin(period_weeks))
            ]

            # Ensure we have data for all 12 weeks for this country
            if len(country_data) == required_period_length:
                avg_ratio = country_data['delivery_ratio'].mean()
                if not pd.isna(avg_ratio) and avg_ratio > 0:
                    period_ratios[country] = avg_ratio
                    valid_countries += 1

        # Only consider periods where ALL countries have COMPLETE data for all 12 weeks
        if valid_countries == len(countries):
            overall_avg = np.mean(list(period_ratios.values()))

            # Score based on distance from 100% (lower is better)
            # Perfect score would be 0 (exactly 100%)
            distance_from_100 = abs(100 - overall_avg)

            # Additional penalty for extreme values (very low or very high ratios)
            if overall_avg < 50 or overall_avg > 150:
                distance_from_100 += 50  # Heavy penalty for unrealistic ratios

            # Bonus for balanced performance across countries (low standard deviation)
            ratio_values = list(period_ratios.values())
            std_dev = np.std(ratio_values)
            balance_bonus = std_dev * 0.1  # Small penalty for unbalanced performance

            final_score = distance_from_100 + balance_bonus

            if final_score < best_score:
                best_score = final_score
                best_period = {
                    'start_week': period_weeks[0],
                    'end_week': period_weeks[-1],
                    'length': required_period_length,
                    'avg_ratios': period_ratios,
                    'overall_avg': overall_avg,
                    'score': final_score,
                    'distance_from_100': distance_from_100,
                    'balance_score': std_dev
                }

    return best_period

def find_best_consecutive_period(dept_data, selected_weeks):
    """Legacy function - kept for compatibility"""
    return find_optimal_12week_period(dept_data, selected_weeks)

def add_trendline(x, y):
    """Calculate trendline for the data"""
    if len(x) < 2 or len(y) < 2:
        return x, [0] * len(x)

    x_numeric = x.apply(parse_custom_week)
    try:
        # Remove any NaN values
        valid_indices = ~(pd.isna(x_numeric) | pd.isna(y))
        if valid_indices.sum() < 2:
            return x, [0] * len(x)

        x_clean = x_numeric[valid_indices]
        y_clean = y[valid_indices]

        slope, intercept, _, _, _ = stats.linregress(x_clean, y_clean)
        line = slope * x_numeric + intercept
        return x, line.fillna(0).tolist()
    except Exception as e:
        st.warning(f"Could not calculate trendline: {str(e)}")
        return x, [0] * len(x)

def main():
    # Main header
    st.markdown('<h1 class="main-header">📊 Sales and Deliveries Dashboard</h1>', unsafe_allow_html=True)

    # Load data
    df = load_data()
    if df is None:
        return

    # Advanced week selection at top of sidebar (applies to both tabs)
    selected_weeks = create_advanced_week_selector(df)

    # Create tabs
    tab1, tab2 = st.tabs(["🌍 Country Analysis", "🏢 Department Analysis"])

    with tab1:
        country_analysis_tab(df, selected_weeks)

    with tab2:
        department_analysis_tab(df, selected_weeks)

def country_analysis_tab(df, selected_weeks):
    # Add country analysis specific filters
    st.sidebar.header("🔧 Country Analysis Filters")
    
    # Department filter
    departments = ['Total'] + sorted(df['dep name'].dropna().unique().tolist())
    selected_department = st.sidebar.selectbox(
        "Select Department:",
        departments,
        index=0
    )

    # Add filter summary
    st.sidebar.markdown("---")
    st.sidebar.subheader("📊 Filter Summary")
    st.sidebar.write(f"**Department:** {selected_department}")
    st.sidebar.write(f"**Weeks:** {len(selected_weeks)} weeks selected")
    st.sidebar.write(f"**Countries:** {len(df['country'].unique())} countries")

    # Filter data based on selections
    filtered_df = df[df['week'].isin(selected_weeks)].copy()
    
    if selected_department != 'Total':
        filtered_df = filtered_df[filtered_df['dep name'] == selected_department]
    
    # Main content area
    if len(filtered_df) == 0:
        st.warning("No data available for the selected filters.")
        return
    
    # Summary metrics
    st.subheader("📈 Summary Metrics")
    col1, col2, col3 = st.columns(3)
    
    total_sold = filtered_df['sold_units'].sum()
    total_delivered = filtered_df['unit_delivered'].sum()
    
    with col1:
        st.metric(
            label="Total Sold Units",
            value=format_large_number(total_sold),
            delta=None
        )
    
    with col2:
        st.metric(
            label="Total Delivered Units", 
            value=format_large_number(total_delivered),
            delta=None
        )
    
    with col3:
        delivery_ratio = (total_delivered / total_sold * 100) if total_sold > 0 else 0
        st.metric(
            label="Delivery Ratio",
            value=f"{delivery_ratio:.1f}%",
            delta=None
        )
    
    # Chart section
    st.subheader("📊 Sales and Deliveries Analysis")

    # Aggregate data for charting
    if selected_department == 'Total':
        chart_data = filtered_df[filtered_df['dep'] != 'UNA'].groupby(['country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()
    else:
        chart_data = filtered_df.groupby(['country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()

    # Create combined chart with subplots for all countries
    countries = sorted(chart_data['country'].unique())

    if len(countries) > 0:
        # Create combined chart configuration
        combined_config = create_combined_chart_config(chart_data, countries, selected_weeks)
        hct.streamlit_highcharts(combined_config, height=800)



def department_analysis_tab(df, selected_weeks):
    """Department analysis tab with table view of department metrics"""

    # Add department analysis specific filters
    st.sidebar.header("🔧 Department Analysis Filters")

    # PMG breakdown toggle
    show_pmg_breakdown = st.sidebar.checkbox(
        "Show PMG Level",
        value=False,
        help="When enabled, shows PMG-level breakdown by country instead of department level"
    )

    # Filter data by week range (show all departments)
    filtered_df = df[df['week'].isin(selected_weeks)].copy()

    if len(filtered_df) == 0:
        st.warning("No data available for the selected filters.")
        return

    # Create and display the table
    if show_pmg_breakdown:
        st.subheader("📊 All Departments - PMG Level Breakdown")
        table_data = create_all_pmg_table_by_country(filtered_df)
    else:
        st.subheader("📊 All Departments Summary")
        table_data = create_all_departments_table_by_country(filtered_df)

    # Excel download button
    if table_data is not None:
        excel_buffer = create_excel_download(table_data, show_pmg_breakdown)
        st.download_button(
            label="📥 Download as Excel",
            data=excel_buffer,
            file_name=f"department_analysis_{'pmg' if show_pmg_breakdown else 'dept'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

    # Recommendation System
    st.subheader("🎯 Recommendation System")
    st.markdown("**Find optimal 12-week periods where delivery ratios are closest to 100% across all countries**")

    # Use form to avoid automatic recalculation
    with st.form("recommendation_form"):
        st.markdown("Click the button below to analyze optimal 12-week periods:")
        submitted = st.form_submit_button("🔍 Analyze Optimal Periods", use_container_width=True)

        if submitted:
            create_recommendation_system(filtered_df, selected_weeks)

def create_all_departments_table_by_country(filtered_df):
    """Create table showing all departments with country breakdown"""

    # Group by department and country
    dept_summary = filtered_df.groupby(['dep name', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
    dept_summary['delivery_ratio'] = (dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100).round(1)

    # Pivot to show countries as columns
    sold_pivot = dept_summary.pivot(index='dep name', columns='country', values='sold_units').fillna(0)
    delivered_pivot = dept_summary.pivot(index='dep name', columns='country', values='unit_delivered').fillna(0)
    ratio_pivot = dept_summary.pivot(index='dep name', columns='country', values='delivery_ratio').fillna(0)

    # Create combined table
    combined_data = []
    for dept in sold_pivot.index:
        row = {'Department': dept}
        for country in ['CZ', 'HU', 'SK']:
            if country in sold_pivot.columns:
                sold = sold_pivot.loc[dept, country]
                delivered = delivered_pivot.loc[dept, country]
                ratio = ratio_pivot.loc[dept, country]

                row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
        combined_data.append(row)

    result_df = pd.DataFrame(combined_data)

    # Apply styling to delivery ratio columns
    def highlight_ratio_columns(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)

        # Apply styling to ratio columns
        for col in df.columns:
            if 'Ratio %' in col:
                styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'

        return styles

    styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
    st.dataframe(styled_df, use_container_width=True, height=500)

    return result_df

def create_all_pmg_table_by_country(filtered_df):
    """Create table showing all PMGs with country breakdown"""

    # Check if PMG column exists
    if 'pmg_name_total' not in filtered_df.columns:
        st.warning("PMG data not available in the dataset.")
        return

    # Group by department, PMG and country
    pmg_summary = filtered_df.groupby(['dep name', 'pmg_name_total', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
    pmg_summary['delivery_ratio'] = (pmg_summary['unit_delivered'] / pmg_summary['sold_units'] * 100).round(1)

    # Pivot to show countries as columns
    sold_pivot = pmg_summary.pivot_table(index=['dep name', 'pmg_name_total'], columns='country', values='sold_units', fill_value=0)
    delivered_pivot = pmg_summary.pivot_table(index=['dep name', 'pmg_name_total'], columns='country', values='unit_delivered', fill_value=0)
    ratio_pivot = pmg_summary.pivot_table(index=['dep name', 'pmg_name_total'], columns='country', values='delivery_ratio', fill_value=0)

    # Create combined table
    combined_data = []
    for (dept, pmg) in sold_pivot.index:
        row = {'Department': dept, 'PMG Name': pmg}
        for country in ['CZ', 'HU', 'SK']:
            if country in sold_pivot.columns:
                sold = sold_pivot.loc[(dept, pmg), country]
                delivered = delivered_pivot.loc[(dept, pmg), country]
                ratio = ratio_pivot.loc[(dept, pmg), country]

                row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
        combined_data.append(row)

    result_df = pd.DataFrame(combined_data)

    # Apply styling to delivery ratio columns
    def highlight_ratio_columns(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)

        # Apply styling to ratio columns
        for col in df.columns:
            if 'Ratio %' in col:
                styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'

        return styles

    styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
    st.dataframe(styled_df, use_container_width=True, height=600)

    return result_df

def create_department_table_by_country(filtered_df):
    """Create department-level summary table by country"""

    # Group by country
    dept_summary = filtered_df.groupby('country')[['sold_units', 'unit_delivered']].sum().reset_index()
    dept_summary['delivery_ratio'] = (dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100).round(1)

    # Format numbers
    dept_summary['sold_units_formatted'] = dept_summary['sold_units'].apply(lambda x: f"{x:,.0f}")
    dept_summary['delivered_units_formatted'] = dept_summary['unit_delivered'].apply(lambda x: f"{x:,.0f}")
    dept_summary['ratio_formatted'] = dept_summary['delivery_ratio'].apply(lambda x: f"{x:.1f}%")

    # Display table with styling
    display_df = dept_summary[['country', 'sold_units_formatted', 'delivered_units_formatted', 'ratio_formatted']].copy()
    display_df.columns = ['Country', 'Sold Units', 'Delivered Units', 'Delivery Ratio']

    # Apply styling to delivery ratio column
    def highlight_ratio_column(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)
        styles['Delivery Ratio'] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'
        return styles

    styled_df = display_df.style.apply(highlight_ratio_column, axis=None)
    st.dataframe(styled_df, use_container_width=True, hide_index=True)

def create_pmg_table_by_country(filtered_df):
    """Create PMG-level summary table by country"""

    # Check if PMG column exists
    if 'pmg_name_total' not in filtered_df.columns:
        st.warning("PMG data not available in the dataset.")
        return

    # Group by PMG and country
    pmg_summary = filtered_df.groupby(['pmg_name_total', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
    pmg_summary['delivery_ratio'] = (pmg_summary['unit_delivered'] / pmg_summary['sold_units'] * 100).round(1)

    # Pivot to show countries as columns
    sold_pivot = pmg_summary.pivot(index='pmg_name_total', columns='country', values='sold_units').fillna(0)
    delivered_pivot = pmg_summary.pivot(index='pmg_name_total', columns='country', values='unit_delivered').fillna(0)
    ratio_pivot = pmg_summary.pivot(index='pmg_name_total', columns='country', values='delivery_ratio').fillna(0)

    # Create combined table
    combined_data = []
    for pmg in sold_pivot.index:
        row = {'PMG Name': pmg}
        for country in ['CZ', 'HU', 'SK']:
            if country in sold_pivot.columns:
                sold = sold_pivot.loc[pmg, country]
                delivered = delivered_pivot.loc[pmg, country]
                ratio = ratio_pivot.loc[pmg, country]

                row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
        combined_data.append(row)

    result_df = pd.DataFrame(combined_data)

    # Apply styling to delivery ratio columns
    def highlight_ratio_columns(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)

        # Apply styling to ratio columns
        for col in df.columns:
            if 'Ratio %' in col:
                styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'

        return styles

    styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
    st.dataframe(styled_df, use_container_width=True, height=400)

def create_department_table(filtered_df, show_all_departments):
    """Create department-level summary table"""

    if show_all_departments:
        # Group by department and country
        dept_summary = filtered_df.groupby(['dep name', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()

        # Calculate delivery ratio
        dept_summary['delivery_ratio'] = (dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100).round(1)

        # Pivot to show countries as columns
        sold_pivot = dept_summary.pivot(index='dep name', columns='country', values='sold_units').fillna(0)
        delivered_pivot = dept_summary.pivot(index='dep name', columns='country', values='unit_delivered').fillna(0)
        ratio_pivot = dept_summary.pivot(index='dep name', columns='country', values='delivery_ratio').fillna(0)

        # Create combined table
        combined_data = []
        for dept in sold_pivot.index:
            row = {'Department': dept}
            for country in ['CZ', 'HU', 'SK']:
                if country in sold_pivot.columns:
                    sold = sold_pivot.loc[dept, country]
                    delivered = delivered_pivot.loc[dept, country]
                    ratio = ratio_pivot.loc[dept, country]

                    row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                    row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                    row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
            combined_data.append(row)

        result_df = pd.DataFrame(combined_data)

        # Apply styling to delivery ratio columns
        def highlight_ratio_columns(df):
            # Create a style dataframe with same shape as result_df
            styles = pd.DataFrame('', index=df.index, columns=df.columns)

            # Apply styling to ratio columns
            for col in df.columns:
                if 'Ratio %' in col:
                    styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'

            return styles

        styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
        st.dataframe(styled_df, use_container_width=True, height=400)

    else:
        # Single department view by country
        dept_summary = filtered_df.groupby('country')[['sold_units', 'unit_delivered']].sum().reset_index()
        dept_summary['delivery_ratio'] = (dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100).round(1)

        # Format numbers
        dept_summary['sold_units_formatted'] = dept_summary['sold_units'].apply(lambda x: f"{x:,.0f}")
        dept_summary['delivered_units_formatted'] = dept_summary['unit_delivered'].apply(lambda x: f"{x:,.0f}")
        dept_summary['ratio_formatted'] = dept_summary['delivery_ratio'].apply(lambda x: f"{x:.1f}%")

        # Display table with styling
        display_df = dept_summary[['country', 'sold_units_formatted', 'delivered_units_formatted', 'ratio_formatted']].copy()
        display_df.columns = ['Country', 'Sold Units', 'Delivered Units', 'Delivery Ratio']

        # Apply styling to delivery ratio column
        def highlight_ratio_column(df):
            styles = pd.DataFrame('', index=df.index, columns=df.columns)
            styles['Delivery Ratio'] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'
            return styles

        styled_df = display_df.style.apply(highlight_ratio_column, axis=None)
        st.dataframe(styled_df, use_container_width=True, hide_index=True)

def create_pmg_table(filtered_df, show_all_departments):
    """Create PMG-level summary table"""

    # Check if PMG column exists
    if 'pmg_name_total' not in filtered_df.columns:
        st.warning("PMG data not available in the dataset.")
        return

    if show_all_departments:
        # Group by department, PMG, and country
        pmg_summary = filtered_df.groupby(['dep name', 'pmg_name_total', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()

        # Calculate delivery ratio
        pmg_summary['delivery_ratio'] = (pmg_summary['unit_delivered'] / pmg_summary['sold_units'] * 100).round(1)

        # Create summary by department and PMG
        pmg_dept_summary = pmg_summary.groupby(['dep name', 'pmg_name_total'])[['sold_units', 'unit_delivered']].sum().reset_index()
        pmg_dept_summary['delivery_ratio'] = (pmg_dept_summary['unit_delivered'] / pmg_dept_summary['sold_units'] * 100).round(1)

        # Format numbers
        pmg_dept_summary['sold_units_formatted'] = pmg_dept_summary['sold_units'].apply(lambda x: f"{x:,.0f}")
        pmg_dept_summary['delivered_units_formatted'] = pmg_dept_summary['unit_delivered'].apply(lambda x: f"{x:,.0f}")
        pmg_dept_summary['ratio_formatted'] = pmg_dept_summary['delivery_ratio'].apply(lambda x: f"{x:.1f}%")

        # Display table with styling
        display_df = pmg_dept_summary[['dep name', 'pmg_name_total', 'sold_units_formatted', 'delivered_units_formatted', 'ratio_formatted']].copy()
        display_df.columns = ['Department', 'PMG Name', 'Sold Units', 'Delivered Units', 'Delivery Ratio']

        # Apply styling to delivery ratio column
        def highlight_ratio_column(df):
            styles = pd.DataFrame('', index=df.index, columns=df.columns)
            styles['Delivery Ratio'] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'
            return styles

        styled_df = display_df.style.apply(highlight_ratio_column, axis=None)
        st.dataframe(styled_df, use_container_width=True, height=500, hide_index=True)

    else:
        # Single department PMG breakdown
        pmg_summary = filtered_df.groupby(['pmg_name_total', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
        pmg_summary['delivery_ratio'] = (pmg_summary['unit_delivered'] / pmg_summary['sold_units'] * 100).round(1)

        # Summary by PMG only
        pmg_total_summary = pmg_summary.groupby('pmg_name_total')[['sold_units', 'unit_delivered']].sum().reset_index()
        pmg_total_summary['delivery_ratio'] = (pmg_total_summary['unit_delivered'] / pmg_total_summary['sold_units'] * 100).round(1)

        # Format numbers
        pmg_total_summary['sold_units_formatted'] = pmg_total_summary['sold_units'].apply(lambda x: f"{x:,.0f}")
        pmg_total_summary['delivered_units_formatted'] = pmg_total_summary['unit_delivered'].apply(lambda x: f"{x:,.0f}")
        pmg_total_summary['ratio_formatted'] = pmg_total_summary['delivery_ratio'].apply(lambda x: f"{x:.1f}%")

        # Display table with styling
        display_df = pmg_total_summary[['pmg_name_total', 'sold_units_formatted', 'delivered_units_formatted', 'ratio_formatted']].copy()
        display_df.columns = ['PMG Name', 'Sold Units', 'Delivered Units', 'Delivery Ratio']

        # Apply styling to delivery ratio column
        def highlight_ratio_column(df):
            styles = pd.DataFrame('', index=df.index, columns=df.columns)
            styles['Delivery Ratio'] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'
            return styles

        styled_df = display_df.style.apply(highlight_ratio_column, axis=None)
        st.dataframe(styled_df, use_container_width=True, height=400, hide_index=True)



def create_highcharts_config(country, weeks, sold_units, delivered_units, sold_trend, delivered_trend):
    """Create Highcharts configuration"""

    # Color scheme based on country
    colors = {
        'CZ': ['#4e79a7', '#a0cbe8'],
        'HU': ['#59a14f', '#8cd17d'],
        'SK': ['#9c755f', '#c9b18f']
    }

    country_colors = colors.get(country, ['#1f77b4', '#aec7e8'])

    # Ensure all data lists have the same length
    data_length = len(weeks)
    sold_units = sold_units[:data_length] + [0] * max(0, data_length - len(sold_units))
    delivered_units = delivered_units[:data_length] + [0] * max(0, data_length - len(delivered_units))
    sold_trend = sold_trend[:data_length] + [0] * max(0, data_length - len(sold_trend))
    delivered_trend = delivered_trend[:data_length] + [0] * max(0, data_length - len(delivered_trend))

    config = {
        "title": {
            "text": f"Sales and Deliveries - {country}",
            "style": {"fontSize": "18px", "fontWeight": "bold"}
        },
        "xAxis": {
            "categories": weeks,
            "title": {"text": "Week"},
            "labels": {"rotation": -45}
        },
        "yAxis": {
            "title": {"text": "Units"},
            "min": 0
        },
        "legend": {
            "align": "center",
            "verticalAlign": "top",
            "layout": "horizontal"
        },
        "plotOptions": {
            "column": {
                "grouping": True,
                "shadow": False,
                "borderWidth": 0,
                "states": {
                    "hover": {
                        "brightness": 0.1,
                        "shadow": False
                    }
                }
            },
            "spline": {
                "states": {
                    "hover": {
                        "lineWidthPlus": 1
                    }
                }
            }
        },
        "series": [
            {
                "type": "column",
                "name": "Sold Units",
                "data": sold_units,
                "color": country_colors[0],
                "opacity": 0.8
            },
            {
                "type": "column",
                "name": "Units Delivered",
                "data": delivered_units,
                "color": country_colors[1],
                "opacity": 0.8
            },
            {
                "type": "spline",
                "name": "Sold Units Trend",
                "data": sold_trend,
                "color": "#FFA500",
                "lineWidth": 2,
                "marker": {"enabled": False}
            },
            {
                "type": "spline",
                "name": "Units Delivered Trend",
                "data": delivered_trend,
                "color": "#FF4136",
                "lineWidth": 2,
                "marker": {"enabled": False}
            }
        ],
        "tooltip": {
            "enabled": True,
            "shared": True,
            "crosshairs": True,
            "backgroundColor": "white",
            "borderColor": "#ccc",
            "borderRadius": 3,
            "shadow": False,
            "style": {
                "fontSize": "12px"
            },
            "pointFormat": "<span style='color:{series.color}'>{series.name}</span>: <b>{point.y:,.0f}</b><br/>",
            "headerFormat": "<b>{point.x}</b><br/>"
        }
    }

    return config

def create_combined_chart_config(chart_data, countries, filtered_weeks):
    """Create combined chart configuration with subplots for all countries"""

    # Color scheme for countries
    colors = {
        'CZ': ['#4e79a7', '#a0cbe8'],
        'HU': ['#59a14f', '#8cd17d'],
        'SK': ['#9c755f', '#c9b18f']
    }

    # Get all weeks for consistent x-axis
    all_weeks_in_range = sorted(filtered_weeks, key=parse_custom_week)
    week_labels = [format_week_label(w) for w in all_weeks_in_range]

    # Prepare series data for all countries
    series_data = []

    for i, country in enumerate(countries):
        country_data = chart_data[chart_data['country'] == country].copy()
        country_data = country_data.sort_values('week', key=lambda x: x.apply(parse_custom_week))

        # Create a complete dataset with all weeks (fill missing weeks with 0)
        week_to_sold = dict(zip(country_data['week'], country_data['sold_units']))
        week_to_delivered = dict(zip(country_data['week'], country_data['unit_delivered']))

        sold_units = [week_to_sold.get(week, 0) for week in all_weeks_in_range]
        delivered_units = [week_to_delivered.get(week, 0) for week in all_weeks_in_range]

        # Calculate trend lines
        if len(country_data) > 1:
            _, sold_trend = add_trendline(country_data['week'], country_data['sold_units'])
            _, delivered_trend = add_trendline(country_data['week'], country_data['unit_delivered'])
        else:
            sold_trend = [0] * len(all_weeks_in_range)
            delivered_trend = [0] * len(all_weeks_in_range)

        country_colors = colors.get(country, ['#1f77b4', '#aec7e8'])

        # Add series for this country
        series_data.extend([
            {
                "type": "column",
                "name": f"{country} - Sold Units",
                "data": sold_units,
                "color": country_colors[0],
                "opacity": 0.8,
                "yAxis": i
            },
            {
                "type": "column",
                "name": f"{country} - Units Delivered",
                "data": delivered_units,
                "color": country_colors[1],
                "opacity": 0.8,
                "yAxis": i
            },
            {
                "type": "spline",
                "name": f"{country} - Sold Trend",
                "data": sold_trend,
                "color": "#FFA500",
                "lineWidth": 2,
                "marker": {"enabled": False},
                "yAxis": i
            },
            {
                "type": "spline",
                "name": f"{country} - Delivered Trend",
                "data": delivered_trend,
                "color": "#FF4136",
                "lineWidth": 2,
                "marker": {"enabled": False},
                "yAxis": i
            }
        ])

    # Create y-axes for each country
    y_axes = []
    for i, country in enumerate(countries):
        y_axes.append({
            "title": {"text": f"{country} - Units"},
            "top": f"{i * 33.33}%",
            "height": "30%",
            "offset": 0,
            "min": 0
        })

    config = {
        "title": {
            "text": "Sales and Deliveries - All Countries",
            "style": {"fontSize": "20px", "fontWeight": "bold"}
        },
        "xAxis": {
            "categories": week_labels,
            "title": {"text": "Week"},
            "labels": {"rotation": -45}
        },
        "yAxis": y_axes,
        "legend": {
            "align": "right",
            "verticalAlign": "top",
            "layout": "vertical",
            "x": -10,
            "y": 100
        },
        "plotOptions": {
            "column": {
                "grouping": True,
                "shadow": False,
                "borderWidth": 0,
                "states": {
                    "hover": {
                        "brightness": 0.1,
                        "shadow": False
                    }
                }
            },
            "spline": {
                "states": {
                    "hover": {
                        "lineWidthPlus": 1
                    }
                }
            }
        },
        "series": series_data,
        "tooltip": {
            "enabled": True,
            "shared": True,
            "crosshairs": True,
            "backgroundColor": "white",
            "borderColor": "#ccc",
            "borderRadius": 3,
            "shadow": False,
            "style": {
                "fontSize": "12px"
            },
            "pointFormat": "<span style='color:{series.color}'>{series.name}</span>: <b>{point.y:,.0f}</b><br/>",
            "headerFormat": "<b>{point.x}</b><br/>"
        }
    }

    return config



def create_pmg_chart_config(pmg_data, pmgs, filtered_weeks):
    """Create chart configuration for PMG breakdown"""

    # Format weeks for display
    formatted_weeks = [format_week_label(week) for week in filtered_weeks]

    # Prepare series data for each PMG
    series_data = []

    # Color palette for PMGs
    colors = ['#4e79a7', '#f28e2c', '#e15759', '#76b7b2', '#59a14f', '#edc949',
              '#af7aa1', '#ff9d9a', '#9c755f', '#bab0ab']

    for i, pmg in enumerate(pmgs[:10]):  # Limit to top 10 PMGs for readability
        pmg_subset = pmg_data[pmg_data['pmg_name_total'] == pmg].copy()
        pmg_subset = pmg_subset.sort_values('week', key=lambda x: x.apply(parse_custom_week))

        # Create complete dataset with all weeks
        week_to_sold = dict(zip(pmg_subset['week'], pmg_subset['sold_units']))
        week_to_delivered = dict(zip(pmg_subset['week'], pmg_subset['unit_delivered']))

        sold_units = [week_to_sold.get(week, 0) for week in filtered_weeks]
        delivered_units = [week_to_delivered.get(week, 0) for week in filtered_weeks]

        # Add sold units series
        series_data.append({
            "type": "column",
            "name": f"{pmg} - Sold Units",
            "data": sold_units,
            "color": colors[i % len(colors)],
            "yAxis": 0
        })

        # Add delivered units series
        series_data.append({
            "type": "column",
            "name": f"{pmg} - Delivered Units",
            "data": delivered_units,
            "color": colors[i % len(colors)],
            "opacity": 0.7,
            "yAxis": 0
        })

    config = {
        "title": {"text": "PMG Breakdown - Sold vs Delivered Units"},
        "xAxis": {"categories": formatted_weeks},
        "yAxis": {
            "title": {"text": "Units"},
            "labels": {"formatter": "function() { return Highcharts.numberFormat(this.value, 0); }"}
        },
        "legend": {
            "align": "right",
            "verticalAlign": "top",
            "layout": "vertical"
        },
        "plotOptions": {
            "column": {
                "grouping": True,
                "shadow": False,
                "borderWidth": 0,
                "states": {
                    "hover": {
                        "brightness": 0.1,
                        "shadow": False
                    }
                }
            }
        },
        "tooltip": {
            "enabled": True,
            "shared": True,
            "crosshairs": True,
            "backgroundColor": "white",
            "borderColor": "#ccc",
            "borderRadius": 3,
            "shadow": False,
            "style": {
                "fontSize": "12px"
            },
            "pointFormat": "<span style='color:{series.color}'>{series.name}</span>: <b>{point.y:,.0f}</b><br/>",
            "headerFormat": "<b>{point.x}</b><br/>"
        },
        "series": series_data
    }

    return config

if __name__ == "__main__":
    main()

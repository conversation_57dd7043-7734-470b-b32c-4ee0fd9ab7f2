"""Data processing utilities for the Sales and Deliveries Dashboard."""

import pandas as pd
import numpy as np
import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
from scipy import stats
import io
from datetime import datetime

class DataProcessor:
    """Enhanced data processing utilities with caching and error handling."""
    
    def __init__(self, df: pd.DataFrame):
        self.df = df.copy()
        self._validate_data()
    
    def _validate_data(self) -> None:
        """Validate and clean the input data."""
        required_columns = ['country', 'week', 'dep name', 'sold_units', 'unit_delivered']
        missing_columns = [col for col in required_columns if col not in self.df.columns]
        
        if missing_columns:
            st.error(f"Missing required columns: {missing_columns}")
            st.stop()
        
        # Clean data
        self.df = self.df.dropna(subset=['country', 'week', 'dep name'])
        self.df['sold_units'] = pd.to_numeric(self.df['sold_units'], errors='coerce').fillna(0)
        self.df['unit_delivered'] = pd.to_numeric(self.df['unit_delivered'], errors='coerce').fillna(0)
        
        # Remove rows with zero sold units to avoid division errors
        self.df = self.df[self.df['sold_units'] > 0]
    
    @staticmethod
    def parse_custom_week(week_str: str) -> int:
        """Parse custom week format to numeric value for sorting."""
        if pd.isna(week_str):
            return 0
        try:
            year = int(week_str[1:5])
            week = int(week_str[6:8])
            return year * 100 + week
        except (ValueError, IndexError, TypeError):
            return 0
    
    @staticmethod  
    def format_week_label(week_str: str) -> str:
        """Format week string for display."""
        if pd.isna(week_str):
            return ""
        try:
            year = week_str[3:5]
            week = week_str[6:8]
            return f"{year}w{week}"
        except (IndexError, TypeError):
            return str(week_str)
    
    @staticmethod
    def format_large_number(num: float) -> str:
        """Format large numbers with K/M suffixes."""
        if pd.isna(num):
            return "0"
        if num >= 1_000_000:
            return f"{num/1_000_000:.1f}M"
        elif num >= 1_000:
            return f"{num/1_000:.1f}K"
        else:
            return f"{num:.0f}"
    
    def get_week_range(self, weeks: List[str]) -> Dict[str, Any]:
        """Get comprehensive week range information."""
        if not weeks:
            return {}
        
        sorted_weeks = sorted(weeks, key=self.parse_custom_week)
        
        return {
            'start_week': sorted_weeks[0],
            'end_week': sorted_weeks[-1],
            'start_formatted': self.format_week_label(sorted_weeks[0]),
            'end_formatted': self.format_week_label(sorted_weeks[-1]),
            'total_weeks': len(weeks),
            'sorted_weeks': sorted_weeks
        }
    
    def filter_data(self, countries: List[str] = None, departments: List[str] = None,
                   weeks: List[str] = None, min_sold_units: float = 0) -> pd.DataFrame:
        """Filter data with multiple criteria."""
        filtered_df = self.df.copy()
        
        if countries:
            filtered_df = filtered_df[filtered_df['country'].isin(countries)]
        
        if departments:
            filtered_df = filtered_df[filtered_df['dep name'].isin(departments)]
        
        if weeks:
            filtered_df = filtered_df[filtered_df['week'].isin(weeks)]
        
        if min_sold_units > 0:
            filtered_df = filtered_df[filtered_df['sold_units'] >= min_sold_units]
        
        return filtered_df
    
    def aggregate_data(self, df: pd.DataFrame, group_by: List[str], 
                      include_ratios: bool = True) -> pd.DataFrame:
        """Aggregate data with optional ratio calculations."""
        
        agg_dict = {
            'sold_units': 'sum',
            'unit_delivered': 'sum'
        }
        
        aggregated = df.groupby(group_by).agg(agg_dict).reset_index()
        
        if include_ratios:
            aggregated['delivery_ratio'] = (
                aggregated['unit_delivered'] / aggregated['sold_units'] * 100
            ).round(2)
            
            # Add ratio categories
            aggregated['ratio_category'] = pd.cut(
                aggregated['delivery_ratio'],
                bins=[0, 80, 100, 120, float('inf')],
                labels=['Under-delivered', 'Good', 'Excellent', 'Over-delivered']
            )
        
        return aggregated
    
    def calculate_trends(self, df: pd.DataFrame, week_col: str = 'week',
                        value_cols: List[str] = ['sold_units', 'unit_delivered']) -> Dict[str, Any]:
        """Calculate trend lines and statistics."""
        
        if len(df) < 2:
            return {col: {'trend': [0] * len(df), 'slope': 0, 'r_squared': 0} 
                   for col in value_cols}
        
        # Sort by week
        df_sorted = df.sort_values(week_col, key=lambda x: x.apply(self.parse_custom_week))
        x_numeric = df_sorted[week_col].apply(self.parse_custom_week)
        
        trends = {}
        
        for col in value_cols:
            if col not in df_sorted.columns:
                continue
                
            y_values = df_sorted[col]
            
            try:
                # Remove any NaN values
                valid_indices = ~(pd.isna(x_numeric) | pd.isna(y_values))
                if valid_indices.sum() < 2:
                    trends[col] = {'trend': [0] * len(df_sorted), 'slope': 0, 'r_squared': 0}
                    continue
                
                x_clean = x_numeric[valid_indices]
                y_clean = y_values[valid_indices]
                
                slope, intercept, r_value, p_value, std_err = stats.linregress(x_clean, y_clean)
                
                # Calculate trend line for all points
                trend_line = slope * x_numeric + intercept
                
                trends[col] = {
                    'trend': trend_line.fillna(0).tolist(),
                    'slope': slope,
                    'intercept': intercept,
                    'r_squared': r_value ** 2,
                    'p_value': p_value,
                    'std_err': std_err
                }
                
            except Exception as e:
                st.warning(f"Could not calculate trend for {col}: {str(e)}")
                trends[col] = {'trend': [0] * len(df_sorted), 'slope': 0, 'r_squared': 0}
        
        return trends
    
    def create_summary_statistics(self, df: pd.DataFrame, 
                                group_by: List[str] = ['country']) -> pd.DataFrame:
        """Create comprehensive summary statistics."""
        
        # Basic aggregation
        summary = df.groupby(group_by).agg({
            'sold_units': ['sum', 'mean', 'std', 'min', 'max', 'count'],
            'unit_delivered': ['sum', 'mean', 'std', 'min', 'max', 'count']
        }).round(2)
        
        # Flatten column names
        summary.columns = ['_'.join(col).strip() for col in summary.columns]
        summary = summary.reset_index()
        
        # Calculate delivery ratios and additional metrics
        summary['total_delivery_ratio'] = (
            summary['unit_delivered_sum'] / summary['sold_units_sum'] * 100
        ).round(2)
        
        summary['avg_delivery_ratio'] = (
            summary['unit_delivered_mean'] / summary['sold_units_mean'] * 100
        ).round(2)
        
        # Calculate coefficient of variation for volatility
        summary['sold_volatility'] = (
            summary['sold_units_std'] / summary['sold_units_mean'] * 100
        ).round(2)
        
        summary['delivered_volatility'] = (
            summary['unit_delivered_std'] / summary['unit_delivered_mean'] * 100
        ).round(2)
        
        return summary
    
    def detect_anomalies(self, df: pd.DataFrame, method: str = 'iqr',
                        columns: List[str] = ['sold_units', 'unit_delivered']) -> pd.DataFrame:
        """Detect anomalies in the data using various methods."""
        
        anomaly_df = df.copy()
        
        for col in columns:
            if col not in df.columns:
                continue
            
            if method == 'iqr':
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                anomaly_df[f'{col}_anomaly'] = (
                    (df[col] < lower_bound) | (df[col] > upper_bound)
                )
                
            elif method == 'zscore':
                z_scores = np.abs(stats.zscore(df[col]))
                anomaly_df[f'{col}_anomaly'] = z_scores > 3
                
            elif method == 'percentile':
                lower_percentile = df[col].quantile(0.01)
                upper_percentile = df[col].quantile(0.99)
                
                anomaly_df[f'{col}_anomaly'] = (
                    (df[col] < lower_percentile) | (df[col] > upper_percentile)
                )
        
        return anomaly_df
    
    def create_pivot_table(self, df: pd.DataFrame, index: List[str], 
                          columns: List[str], values: str, 
                          aggfunc: str = 'sum') -> pd.DataFrame:
        """Create enhanced pivot table with formatting."""
        
        try:
            pivot = df.pivot_table(
                index=index,
                columns=columns,
                values=values,
                aggfunc=aggfunc,
                fill_value=0
            )
            
            # Add totals
            if len(columns) == 1:
                pivot['Total'] = pivot.sum(axis=1)
                pivot.loc['Total'] = pivot.sum(axis=0)
            
            return pivot
            
        except Exception as e:
            st.error(f"Error creating pivot table: {str(e)}")
            return pd.DataFrame()
    
    def export_processed_data(self, df: pd.DataFrame, filename: str = "processed_data",
                            include_summary: bool = True) -> Dict[str, bytes]:
        """Export processed data in multiple formats."""
        
        exports = {}
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # CSV export
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False)
        exports['csv'] = csv_buffer.getvalue().encode()
        
        # Excel export with multiple sheets
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            # Main data
            df.to_excel(writer, sheet_name='Data', index=False)
            
            # Summary statistics if requested
            if include_summary:
                summary = self.create_summary_statistics(df)
                summary.to_excel(writer, sheet_name='Summary', index=False)
                
                # Country breakdown
                if 'country' in df.columns:
                    country_summary = df.groupby('country').agg({
                        'sold_units': ['sum', 'mean'],
                        'unit_delivered': ['sum', 'mean']
                    }).round(2)
                    country_summary.to_excel(writer, sheet_name='Country_Summary')
        
        exports['excel'] = excel_buffer.getvalue()
        
        # JSON export
        json_data = {
            'data': df.to_dict('records'),
            'metadata': {
                'export_timestamp': timestamp,
                'total_records': len(df),
                'columns': df.columns.tolist(),
                'data_types': df.dtypes.astype(str).to_dict()
            }
        }
        
        import json
        exports['json'] = json.dumps(json_data, indent=2, default=str).encode()
        
        return exports
    
    @st.cache_data
    def get_data_overview(_self) -> Dict[str, Any]:
        """Get cached data overview for performance."""
        df = _self.df
        
        # Only check numeric columns for negative values
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        return {
            'total_records': len(df),
            'date_range': {
                'start': df['week'].min(),
                'end': df['week'].max(),
                'total_weeks': df['week'].nunique()
            },
            'countries': sorted(df['country'].unique().tolist()),
            'departments': sorted(df['dep name'].unique().tolist()),
            'total_sold_units': df['sold_units'].sum(),
            'total_delivered_units': df['unit_delivered'].sum(),
            'overall_delivery_ratio': (df['unit_delivered'].sum() / df['sold_units'].sum() * 100) if df['sold_units'].sum() > 0 else 0,
            'data_quality': {
                'missing_values': df.isnull().sum().to_dict(),
                'zero_values': (df[numeric_columns] == 0).sum().to_dict(),
                'negative_values': (df[numeric_columns] < 0).sum().to_dict()
            }
        }
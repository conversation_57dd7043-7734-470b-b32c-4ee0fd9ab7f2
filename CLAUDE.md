# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an **Enhanced Sales and Deliveries Dashboard** project that analyzes the relationship between sold units and delivered units across three countries (CZ, HU, SK) over time. The project features a **modern dark-themed Streamlit application** with advanced analytics, AI-powered recommendations, and sophisticated UI components.

## Key Commands

### Running the Enhanced Application
```bash
streamlit run main.py        # New enhanced dark-themed application
streamlit run streamlit_app.py  # Original application (backup)
```
Access at `http://localhost:8501`

### Testing
```bash
python test_app.py          # Test data loading and basic functionality
python test_week_format.py  # Test week parsing functions
python test_tooltip.py      # Test tooltip functionality
python examine_data.py      # Explore data structure and content
```

### Dependencies Installation
```bash
pip install streamlit pandas numpy scipy streamlit-highcharts openpyxl
```

## Enhanced Architecture & Features

### Modular Architecture
```
├── main.py                          # New enhanced application entry point
├── streamlit_app_original.py        # Original application backup
├── .streamlit/config.toml           # Dark theme configuration
├── config/
│   └── theme_config.py              # Theme colors and settings
├── components/
│   ├── ui_components.py             # Enhanced UI components with glassmorphism
│   └── charts.py                    # Dark-themed Highcharts components
├── utils/
│   ├── data_processing.py           # Advanced data processing utilities
│   ├── recommendation.py            # AI recommendation system
│   └── export_functions.py          # Multi-format export capabilities
└── assets/
    ├── styles.css                   # Custom CSS with animations
    └── animations.css               # Advanced animation definitions
```

### Core Data Structure
- **Data Source**: `24w26_25w26.parquet` - Contains sales and delivery data
- **Key Columns**: 
  - `country` (CZ, HU, SK)
  - `week` (format: 'f2024w26')
  - `dep name` (department name)
  - `sold_units`, `unit_delivered`

### Enhanced Application Features

#### 1. Modern Dark Theme
- **Sophisticated Color Palette**: Cyan-green primary (#00d4aa) on dark backgrounds
- **Glassmorphism Effects**: Translucent containers with blur effects
- **Advanced Animations**: Smooth transitions, hover effects, loading states
- **Responsive Design**: Adapts to different screen sizes

#### 2. AI-Powered Recommendation System
- **Flexible Time Periods**: Configurable analysis from 1-52 weeks
- **Intelligent Optimization**: Finds optimal periods closest to target delivery ratios
- **Advanced Filtering**: Multi-dimensional filtering by country, department, data completeness
- **Export Capabilities**: Multiple format exports (CSV, Excel, JSON, ZIP bundles)

#### 3. Enhanced Navigation
- **Country Overview**: Multi-country analysis with performance metrics
- **Department Deep-dive**: Detailed department analysis with PMG breakdown
- **AI Recommendations**: Advanced recommendation system with configurable parameters
- **Advanced Analytics**: Statistical analysis, anomaly detection, data quality assessment

#### 4. Advanced UI Components
- **Time Period Selector**: Enhanced interface with preset options and custom ranges
- **Animated Metrics Cards**: Glassmorphism metric cards with hover effects
- **Enhanced Data Tables**: Styled dataframes with ratio highlighting
- **Interactive Charts**: Dark-themed Highcharts with improved tooltips and animations

### Key Functions & Components

#### Enhanced Week Handling System
- `DataProcessor.parse_custom_week()`: Converts 'f2024w26' format to numeric
- `DataProcessor.format_week_label()`: User-friendly 'w26' format display
- `create_time_period_selector()`: Advanced time selection with presets

#### Advanced Chart Generation
- `create_enhanced_combined_chart()`: Dark-themed multi-country charts with gradients
- `create_scatter_chart()`: Correlation analysis with country-specific colors
- `render_enhanced_chart()`: Chart rendering with error handling and animations
- **Enhanced Tooltips**: Rich HTML tooltips with better formatting

#### AI Recommendation Engine
- `AdvancedRecommendationSystem`: Configurable period optimization
- Flexible analysis periods (1-52 weeks)
- Target ratio optimization (customizable from 0-200%)
- Multi-dimensional scoring including balance and data completeness
- Advanced filtering and sorting capabilities

#### Data Processing Enhancements
- `DataProcessor`: Comprehensive data validation and cleaning
- Anomaly detection with multiple methods (IQR, Z-score, percentile-based)
- Trend analysis with statistical measures
- Summary statistics with volatility calculations

#### Export System
- `EnhancedExporter`: Multi-format export capabilities
- **Formats**: CSV, Excel (with styling), JSON, ZIP bundles, text reports
- **Excel Enhancements**: Multiple sheets, styling, auto-column sizing
- **ZIP Bundles**: Complete data packages with README files

### Performance Optimizations
- **Caching**: Enhanced `@st.cache_data` usage throughout
- **Lazy Loading**: Efficient data processing with minimal memory usage
- **Modular Design**: Separated concerns for better maintainability
- **Error Handling**: Comprehensive error handling and user feedback

### Dark Theme Implementation
- **Streamlit Config**: Custom theme in `.streamlit/config.toml`
- **CSS Animations**: Advanced animations in `assets/animations.css`
- **Glassmorphism**: Modern UI effects with backdrop-filter
- **Color Consistency**: Unified color scheme across all components

### Migration from Original
The enhanced application (`main.py`) provides:
- **100% Feature Parity**: All original functionality preserved
- **Enhanced UX**: Modern interface with better navigation
- **Advanced Features**: AI recommendations, anomaly detection, advanced exports
- **Better Performance**: Optimized data processing and caching
- **Mobile Responsive**: Better mobile and tablet experience

### Development Guidelines
- **Theme Colors**: Use colors from `config/theme_config.py`
- **UI Components**: Leverage components from `components/ui_components.py`
- **Data Processing**: Use `DataProcessor` class for data operations
- **Export Functions**: Use `EnhancedExporter` for all export needs
- **Error Handling**: Implement comprehensive error handling with user-friendly messages
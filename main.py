"""
Enhanced Sales and Deliveries Dashboard with Modern Dark Theme
A sophisticated Streamlit application for analyzing sales and delivery data with advanced features.
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

try:
    # Import custom modules
    from components.ui_components import (
        load_css, create_animated_header, create_metric_card, 
        create_time_period_selector, create_enhanced_dataframe,
        create_chart_container, create_export_section
    )
    from components.charts import create_enhanced_combined_chart, create_scatter_chart, render_enhanced_chart
    from utils.data_processing import DataProcessor
    from utils.recommendation import AdvancedRecommendationSystem
    from utils.export_functions import exporter
    from config.theme_config import COLORS, COUNTRY_COLORS
except ImportError as e:
    st.error(f"❌ Import error: {e}")
    st.info("Please ensure all files are in the correct directories as specified in CLAUDE.md")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="Sales & Deliveries Dashboard 2.0",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

def initialize_app():
    """Initialize the application with custom styling and data loading."""
    # Load custom CSS
    load_css()
    
    # Create animated header
    create_animated_header(
        "🚀 Sales & Deliveries Dashboard 2.0",
        "Advanced Analytics with Modern Dark Theme & AI-Powered Recommendations"
    )

@st.cache_data
def load_data():
    """Load and cache the parquet data with enhanced error handling."""
    try:
        df = pd.read_parquet("24w26_25w26.parquet")
        
        # Initialize data processor for validation and cleaning
        processor = DataProcessor(df)
        return processor.df
        
    except FileNotFoundError:
        st.error("🚨 Data file '24w26_25w26.parquet' not found. Please ensure the file is in the correct directory.")
        st.info("📁 Expected file location: Same directory as this application")
        return None
    except Exception as e:
        st.error(f"❌ Error loading data: {str(e)}")
        return None

def create_sidebar_navigation():
    """Create enhanced sidebar navigation with modern styling."""
    
    st.sidebar.markdown("""
    <div style="background: rgba(26, 30, 46, 0.8); backdrop-filter: blur(10px); 
                border: 1px solid rgba(0, 212, 170, 0.3); border-radius: 16px; 
                padding: 1.5rem; margin-bottom: 2rem; text-align: center;">
        <h2 style="color: #00d4aa; margin: 0;">🎯 Navigation</h2>
        <p style="color: #fafafa; opacity: 0.8; margin: 0.5rem 0 0 0; font-size: 0.9rem;">
            Choose your analysis focus
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Main navigation tabs
    analysis_mode = st.sidebar.radio(
        "📊 Analysis Mode",
        ["🌍 Country Overview", "🏢 Department Deep-dive", "🎯 AI Recommendations", "📈 Advanced Analytics"],
        index=0
    )
    
    return analysis_mode

def country_overview_tab(df, data_processor):
    """Enhanced country overview with modern UI components."""
    
    st.markdown("""
    <div style="margin: 2rem 0;">
        <h2 style="color: #00d4aa; display: flex; align-items: center;">
            <span style="margin-right: 0.5rem;">🌍</span>
            Country Performance Overview
        </h2>
        <p style="color: #fafafa; opacity: 0.8;">
            Comprehensive analysis across all countries with flexible time period selection
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Enhanced time period selector
    time_config = create_time_period_selector(df, key_prefix="country_")
    selected_weeks = time_config['selected_weeks']
    
    st.sidebar.markdown("---")
    
    # Department filter
    st.sidebar.markdown("### 🔧 Filters")
    departments = ['All Departments'] + sorted(df['dep name'].dropna().unique().tolist())
    selected_department = st.sidebar.selectbox(
        "Department Focus:",
        departments,
        index=0,
        help="Select a specific department or view all combined"
    )
    
    # Advanced filters
    with st.sidebar.expander("🔬 Advanced Filters"):
        min_sold_units = st.number_input(
            "Minimum Sold Units",
            min_value=0,
            value=0,
            step=1000,
            help="Filter out records below this threshold"
        )
        
        countries_filter = st.multiselect(
            "Countries to Include",
            options=['CZ', 'HU', 'SK'],
            default=['CZ', 'HU', 'SK']
        )
    
    # Filter data
    filtered_df = data_processor.filter_data(
        countries=countries_filter,
        departments=None if selected_department == 'All Departments' else [selected_department],
        weeks=selected_weeks,
        min_sold_units=min_sold_units
    )
    
    if len(filtered_df) == 0:
        st.warning("⚠️ No data available with current filters. Please adjust your selection.")
        return
    
    # Summary metrics with enhanced cards
    st.markdown("### 📊 Performance Metrics")
    
    col1, col2, col3, col4 = st.columns(4)
    
    total_sold = filtered_df['sold_units'].sum()
    total_delivered = filtered_df['unit_delivered'].sum()
    delivery_ratio = (total_delivered / total_sold * 100) if total_sold > 0 else 0
    unique_weeks = filtered_df['week'].nunique()
    
    with col1:
        create_metric_card(
            "Total Sold Units",
            DataProcessor.format_large_number(total_sold),
            f"{unique_weeks} weeks",
            "normal",
            "📦"
        )
    
    with col2:
        create_metric_card(
            "Total Delivered",
            DataProcessor.format_large_number(total_delivered),
            f"From {len(countries_filter)} countries",
            "normal",
            "🚚"
        )
    
    with col3:
        ratio_color = "normal" if 80 <= delivery_ratio <= 120 else "inverse"
        create_metric_card(
            "Delivery Ratio",
            f"{delivery_ratio:.1f}%",
            "Target: 100%",
            ratio_color,
            "🎯"
        )
    
    with col4:
        departments_count = filtered_df['dep name'].nunique()
        create_metric_card(
            "Departments",
            str(departments_count),
            f"Active in analysis",
            "normal",
            "🏢"
        )
    
    # Enhanced chart visualization
    create_chart_container(
        lambda: create_country_charts(filtered_df, selected_weeks, countries_filter),
        "📈 Sales & Deliveries Trends"
    )
    
    # Summary table with enhanced formatting
    st.markdown("### 📋 Detailed Breakdown")
    
    summary_df = data_processor.create_summary_statistics(filtered_df, ['country'])
    create_enhanced_dataframe(summary_df, "Country Performance Summary", height=300)
    
    # Export section
    export_data = {
        'summary': summary_df,
        'detailed_data': filtered_df
    }
    
    metadata = {
        'analysis_type': 'Country Overview',
        'time_period': time_config,
        'filters': {
            'department': selected_department,
            'countries': countries_filter,
            'min_sold_units': min_sold_units
        }
    }
    
    exporter.create_download_interface(export_data, metadata, "Country Analysis Export")

def create_country_charts(filtered_df, selected_weeks, countries):
    """Create enhanced country charts with dark theme."""
    
    # Aggregate data for charting
    chart_data = filtered_df.groupby(['country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()
    
    if len(chart_data) > 0:
        # Combined chart
        combined_config = create_enhanced_combined_chart(
            chart_data, 
            countries, 
            selected_weeks,
            "Sales and Deliveries - Multi-Country Analysis"
        )
        render_enhanced_chart(combined_config, height=800, key="country_combined")
        
        # Correlation scatter plot
        st.markdown("### 🔍 Correlation Analysis")
        scatter_config = create_scatter_chart(
            chart_data,
            "Sold vs Delivered Units - Correlation by Country"
        )
        render_enhanced_chart(scatter_config, height=500, key="country_scatter")

def ai_recommendations_tab(df, data_processor):
    """Enhanced AI recommendations with flexible configuration."""
    
    st.markdown("""
    <div style="margin: 2rem 0;">
        <h2 style="color: #00d4aa; display: flex; align-items: center;">
            <span style="margin-right: 0.5rem;">🎯</span>
            AI-Powered Recommendations
        </h2>
        <p style="color: #fafafa; opacity: 0.8;">
            Advanced recommendation system with configurable time periods and intelligent optimization
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize recommendation system
    rec_system = AdvancedRecommendationSystem(df)
    
    # Time period selection
    time_config = create_time_period_selector(df, key_prefix="ai_rec_")
    selected_weeks = time_config['selected_weeks']
    
    st.sidebar.markdown("---")
    
    # Advanced filters interface
    filters = rec_system.create_advanced_filter_interface()
    
    # Analysis trigger
    st.markdown("### 🚀 Run Analysis")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.info(
            f"🔍 **Ready to analyze:** {filters['analysis_period']} week periods "
            f"across {len(filters['countries'])} countries, targeting {filters['target_ratio']}% delivery ratio"
        )
    
    with col2:
        run_analysis = st.button(
            "🎯 Generate Recommendations",
            type="primary",
            use_container_width=True
        )
    
    if run_analysis:
        with st.spinner("🤖 AI is analyzing your data..."):
            results = rec_system.analyze_optimal_periods(
                selected_weeks=selected_weeks,
                analysis_period=filters['analysis_period'],
                target_ratio=filters['target_ratio'],
                countries=filters['countries'],
                departments=filters['departments']
            )
            
            # Display results
            rec_system.display_recommendation_results(results, filters['sort_by'])

def department_deepdive_tab(df, data_processor):
    """Enhanced department analysis with advanced features."""
    
    st.markdown("""
    <div style="margin: 2rem 0;">
        <h2 style="color: #00d4aa; display: flex; align-items: center;">
            <span style="margin-right: 0.5rem;">🏢</span>
            Department Deep-dive Analysis
        </h2>
        <p style="color: #fafafa; opacity: 0.8;">
            Detailed department-level insights with PMG breakdown and trend analysis
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Time period selector
    time_config = create_time_period_selector(df, key_prefix="dept_")
    selected_weeks = time_config['selected_weeks']
    
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 🔧 Department Filters")
    
    # Department selection
    departments = sorted(df['dep name'].dropna().unique().tolist())
    selected_departments = st.sidebar.multiselect(
        "Departments to Analyze:",
        departments,
        default=departments[:5] if len(departments) > 5 else departments,
        help="Select one or more departments for analysis"
    )
    
    # Analysis options
    show_pmg_breakdown = st.sidebar.checkbox(
        "📊 Show PMG Breakdown",
        value=False,
        help="Include Product Management Group level analysis"
    )
    
    show_trends = st.sidebar.checkbox(
        "📈 Include Trend Analysis",
        value=True,
        help="Show statistical trends and forecasting"
    )
    
    # Filter data
    filtered_df = data_processor.filter_data(
        departments=selected_departments,
        weeks=selected_weeks
    )
    
    if len(filtered_df) == 0:
        st.warning("⚠️ No data available for selected departments and time period.")
        return
    
    # Department comparison metrics
    st.markdown("### 📊 Department Performance Comparison")
    
    dept_summary = filtered_df.groupby('dep name').agg({
        'sold_units': 'sum',
        'unit_delivered': 'sum'
    }).reset_index()
    
    dept_summary['delivery_ratio'] = (
        dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100
    ).round(1)
    
    dept_summary = dept_summary.sort_values('delivery_ratio', ascending=False)
    
    # Top performers
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 🏆 Top Performers")
        top_performers = dept_summary.head(3)
        for idx, row in top_performers.iterrows():
            st.success(
                f"**{row['dep name']}**: {row['delivery_ratio']:.1f}% "
                f"({DataProcessor.format_large_number(row['unit_delivered'])} delivered)"
            )
    
    with col2:
        st.markdown("#### 📈 Improvement Opportunities") 
        bottom_performers = dept_summary.tail(3)
        for idx, row in bottom_performers.iterrows():
            st.warning(
                f"**{row['dep name']}**: {row['delivery_ratio']:.1f}% "
                f"({DataProcessor.format_large_number(row['sold_units'] - row['unit_delivered'])} gap)"
            )
    
    # Detailed analysis table
    if show_pmg_breakdown and 'pmg_name_total' in filtered_df.columns:
        st.markdown("### 📋 PMG-Level Analysis")
        
        pmg_data = filtered_df.groupby(['dep name', 'pmg_name_total', 'country']).agg({
            'sold_units': 'sum',
            'unit_delivered': 'sum'
        }).reset_index()
        
        pmg_data['delivery_ratio'] = (
            pmg_data['unit_delivered'] / pmg_data['sold_units'] * 100
        ).round(1)
        
        create_enhanced_dataframe(pmg_data, "PMG Breakdown by Country", height=600)
    
    else:
        st.markdown("### 📋 Department Summary")
        create_enhanced_dataframe(dept_summary, "Department Performance Summary", height=400)
    
    # Export functionality
    export_data = {
        'department_summary': dept_summary
    }
    
    if show_pmg_breakdown and 'pmg_name_total' in filtered_df.columns:
        export_data['pmg_breakdown'] = pmg_data
    
    metadata = {
        'analysis_type': 'Department Deep-dive',
        'selected_departments': selected_departments,
        'time_period': time_config,
        'pmg_breakdown': show_pmg_breakdown
    }
    
    exporter.create_download_interface(export_data, metadata, "Department Analysis Export")

def advanced_analytics_tab(df, data_processor):
    """Advanced analytics with anomaly detection and forecasting."""
    
    st.markdown("""
    <div style="margin: 2rem 0;">
        <h2 style="color: #00d4aa; display: flex; align-items: center;">
            <span style="margin-right: 0.5rem;">📈</span>
            Advanced Analytics Suite
        </h2>
        <p style="color: #fafafa; opacity: 0.8;">
            Statistical analysis, anomaly detection, and predictive insights
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Analysis options
    col1, col2, col3 = st.columns(3)
    
    with col1:
        analysis_type = st.selectbox(
            "📊 Analysis Type",
            ["Data Overview", "Anomaly Detection", "Trend Analysis", "Statistical Summary"]
        )
    
    with col2:
        time_granularity = st.selectbox(
            "⏱️ Time Granularity",
            ["Weekly", "Monthly", "Quarterly"]
        )
    
    with col3:
        metric_focus = st.selectbox(
            "🎯 Metric Focus",
            ["Both", "Sold Units Only", "Delivered Units Only", "Delivery Ratio"]
        )
    
    if analysis_type == "Data Overview":
        st.markdown("### 📋 Data Overview")
        overview = data_processor.get_data_overview()
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            create_metric_card("Total Records", f"{overview['total_records']:,}", "", "normal", "📊")
        
        with col2:
            create_metric_card("Countries", len(overview['countries']), "", "normal", "🌍")
        
        with col3:
            create_metric_card("Departments", len(overview['departments']), "", "normal", "🏢")
        
        with col4:
            create_metric_card("Weeks", overview['date_range']['total_weeks'], "", "normal", "📅")
        
        # Data quality metrics
        st.markdown("### 🔍 Data Quality Assessment")
        
        quality_data = overview['data_quality']
        quality_df = pd.DataFrame([
            {"Metric": "Missing Values", "sold_units": quality_data['missing_values'].get('sold_units', 0), 
             "unit_delivered": quality_data['missing_values'].get('unit_delivered', 0)},
            {"Metric": "Zero Values", "sold_units": quality_data['zero_values'].get('sold_units', 0),
             "unit_delivered": quality_data['zero_values'].get('unit_delivered', 0)},
        ])
        
        create_enhanced_dataframe(quality_df, "Data Quality Metrics")
    
    elif analysis_type == "Anomaly Detection":
        st.markdown("### 🚨 Anomaly Detection")
        
        detection_method = st.selectbox(
            "Detection Method",
            ["IQR (Interquartile Range)", "Z-Score", "Percentile-based"]
        )
        
        method_map = {"IQR (Interquartile Range)": "iqr", "Z-Score": "zscore", "Percentile-based": "percentile"}
        
        anomalies = data_processor.detect_anomalies(df, method=method_map[detection_method])
        
        # Count anomalies
        sold_anomalies = anomalies['sold_units_anomaly'].sum()
        delivered_anomalies = anomalies['unit_delivered_anomaly'].sum()
        
        col1, col2 = st.columns(2)
        
        with col1:
            create_metric_card("Sold Units Anomalies", str(sold_anomalies), 
                             f"{sold_anomalies/len(anomalies)*100:.1f}% of records", "inverse", "⚠️")
        
        with col2:
            create_metric_card("Delivered Units Anomalies", str(delivered_anomalies),
                             f"{delivered_anomalies/len(anomalies)*100:.1f}% of records", "inverse", "🚨")
        
        if sold_anomalies > 0 or delivered_anomalies > 0:
            anomaly_records = anomalies[
                (anomalies['sold_units_anomaly'] == True) | 
                (anomalies['unit_delivered_anomaly'] == True)
            ][['country', 'week', 'dep name', 'sold_units', 'unit_delivered']]
            
            create_enhanced_dataframe(anomaly_records, "Detected Anomalies", height=400)

def main():
    """Main application function with enhanced navigation and features."""
    
    # Initialize app
    initialize_app()
    
    # Load data
    df = load_data()
    if df is None:
        st.stop()
    
    # Initialize data processor
    data_processor = DataProcessor(df)
    
    # Create sidebar navigation
    analysis_mode = create_sidebar_navigation()
    
    # Display data overview in sidebar
    with st.sidebar.expander("📊 Data Overview", expanded=False):
        overview = data_processor.get_data_overview()
        st.metric("Total Records", f"{overview['total_records']:,}")
        st.metric("Date Range", f"{overview['date_range']['total_weeks']} weeks")
        st.metric("Countries", len(overview['countries']))
        st.metric("Departments", len(overview['departments']))
    
    # Route to appropriate tab
    if analysis_mode == "🌍 Country Overview":
        country_overview_tab(df, data_processor)
    
    elif analysis_mode == "🏢 Department Deep-dive":
        department_deepdive_tab(df, data_processor)
    
    elif analysis_mode == "🎯 AI Recommendations":
        ai_recommendations_tab(df, data_processor)
    
    elif analysis_mode == "📈 Advanced Analytics":
        advanced_analytics_tab(df, data_processor)
    
    # Footer
    st.markdown("---")
    st.markdown(
        '<div style="text-align: center; color: #666; font-size: 0.8rem; margin: 2rem 0;">'
        '🚀 Sales & Deliveries Dashboard 2.0 | Enhanced with Modern Dark Theme & AI | '
        f'Last updated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        '</div>',
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
import pandas as pd

def format_week_label(week_str):
    """Format week string for display"""
    if pd.isna(week_str):
        return ""
    # Convert f2024w26 to 24w26
    year = week_str[3:5]  # Get last 2 digits of year
    week = week_str[6:8]  # Get week number
    return f"{year}w{week}"

# Test the week formatting
test_weeks = ['f2024w26', 'f2024w27', 'f2024w52', 'f2025w01', 'f2025w26']
formatted = [format_week_label(w) for w in test_weeks]

print("Week formatting test:")
for original, formatted_week in zip(test_weeks, formatted):
    print(f"{original} -> {formatted_week}")

# Load actual data to test
df = pd.read_parquet("24w26_25w26.parquet")
actual_weeks = sorted(df['week'].unique())
print(f"\nFirst 5 actual weeks: {actual_weeks[:5]}")
print(f"Formatted: {[format_week_label(w) for w in actual_weeks[:5]]}")
print(f"\nLast 5 actual weeks: {actual_weeks[-5:]}")
print(f"Formatted: {[format_week_label(w) for w in actual_weeks[-5:]]}")

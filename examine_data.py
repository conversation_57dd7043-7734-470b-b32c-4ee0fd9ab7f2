import pandas as pd
import numpy as np

# Load the parquet file
df = pd.read_parquet("24w26_25w26.parquet")

print("Data shape:", df.shape)
print("\nColumn names:")
print(df.columns.tolist())
print("\nData types:")
print(df.dtypes)
print("\nFirst few rows:")
print(df.head())
print("\nUnique countries:")
print(df['country'].unique())
print("\nUnique weeks (first 10):")
print(sorted(df['week'].unique())[:10])
print("\nUnique departments (first 10):")
print(sorted(df['dep'].unique())[:10])
print("\nSample data:")
print(df.sample(5))

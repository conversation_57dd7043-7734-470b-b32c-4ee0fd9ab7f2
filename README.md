# Sales and Deliveries Dashboard

A Streamlit web application for analyzing the relationship between sold units and delivered units across different countries, departments, and time periods.

## Features

### 📊 Interactive Visualizations
- **Combined Multi-Country Chart**: Single chart with subplots for all 3 countries (CZ, HU, SK) with shared x-axes, just like the Jupyter notebook
- **Time Series Analysis**: Column charts showing sold units and delivered units over time
- **Trend Lines**: Spline overlays showing statistical trends in the data
- **Scatter Plot**: Relationship visualization between sold and delivered units
- **Country-specific Color Coding**: Different colors for CZ, HU, and SK

### 🔧 Interactive Filters
- **Department Selection**: Dropdown to filter by specific department or view all departments
- **Week Range Selection**: Dropdown selectors showing weeks in '24w26' - '25w26' format for easy selection
- **Real-time Updates**: All charts and metrics update automatically when filters change

### 📈 Summary Metrics
- **Total Sold Units**: Aggregated across selected filters
- **Total Delivered Units**: Aggregated across selected filters  
- **Delivery Ratio**: Percentage of delivered units vs sold units
- **Statistical Summary Table**: Mean, sum, and standard deviation by country

### 💾 Data Export
- **CSV Download**: Export filtered data for further analysis
- **Interactive Data Table**: Browse the summary statistics

## Requirements

```bash
pip install streamlit pandas numpy scipy streamlit-highcharts
```

## Usage

1. **Start the Application**:
   ```bash
   streamlit run streamlit_app.py
   ```

2. **Access the Dashboard**:
   Open your browser to `http://localhost:8501`

3. **Use the Filters**:
   - Select a department from the sidebar dropdown
   - Adjust the week range using the slider
   - Watch as all visualizations update in real-time

4. **Analyze the Data**:
   - View time series trends for each country
   - Examine the scatter plot for sold vs delivered relationships
   - Check summary statistics in the data table
   - Download filtered data for offline analysis

## Data Structure

The application expects a parquet file named `24w26_25w26.parquet` with the following columns:
- `country`: Country code (CZ, HU, SK)
- `week`: Week identifier in format 'f2024w26'
- `dep name`: Department name
- `sold_units`: Number of units sold
- `unit_delivered`: Number of units delivered
- Additional metadata columns

## Technical Implementation

- **Framework**: Streamlit for the web interface
- **Charts**: Streamlit-Highcharts for interactive visualizations
- **Data Processing**: Pandas and NumPy for data manipulation
- **Statistics**: SciPy for trend line calculations
- **Caching**: Streamlit's @st.cache_data for performance optimization

## File Structure

```
├── streamlit_app.py      # Main application file
├── 24w26_25w26.parquet  # Data file
├── test_app.py          # Test script
├── examine_data.py      # Data exploration script
└── README.md           # This documentation
```

## Features Comparison with Jupyter Notebook

This Streamlit app recreates and enhances the analysis from `viz.ipynb`:

✅ **Recreated Features**:
- Time series visualization of sold vs delivered units
- Country-wise analysis (CZ, HU, SK)
- Statistical trend lines
- Data aggregation and filtering

✅ **Enhanced Features**:
- Interactive filtering by department and week range
- Real-time chart updates
- Summary metrics dashboard
- Scatter plot analysis
- Data export functionality
- Professional web interface

## Performance Notes

- Data is cached using Streamlit's caching mechanism
- Large datasets are handled efficiently with pandas
- Charts render smoothly with Highcharts
- Responsive design works on different screen sizes

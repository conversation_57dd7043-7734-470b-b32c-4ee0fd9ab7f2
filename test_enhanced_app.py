"""Test script for the enhanced Sales and Deliveries Dashboard."""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

def test_imports():
    """Test that all modules can be imported successfully."""
    try:
        from components.ui_components import load_css, create_animated_header
        from components.charts import create_enhanced_combined_chart
        from utils.data_processing import DataProcessor
        from utils.recommendation import AdvancedRecommendationSystem
        from utils.export_functions import exporter
        from config.theme_config import COLORS, COUNTRY_COLORS
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_data_loading():
    """Test data loading functionality."""
    try:
        if os.path.exists("24w26_25w26.parquet"):
            df = pd.read_parquet("24w26_25w26.parquet")
            processor = DataProcessor(df)
            print(f"✅ Data loaded successfully: {processor.df.shape}")
            return True, processor.df
        else:
            print("⚠️ Data file not found, creating mock data for testing")
            # Create mock data
            countries = ['CZ', 'HU', 'SK']
            weeks = ['f2024w26', 'f2024w27', 'f2024w28', 'f2024w29', 'f2024w30']
            departments = ['Electronics', 'Clothing', 'Food', 'Home & Garden']
            
            data = []
            for country in countries:
                for week in weeks:
                    for dept in departments:
                        sold = np.random.randint(1000, 10000)
                        delivered = int(sold * np.random.uniform(0.7, 1.3))
                        data.append({
                            'country': country,
                            'week': week,
                            'dep name': dept,
                            'sold_units': sold,
                            'unit_delivered': delivered
                        })
            
            df = pd.DataFrame(data)
            processor = DataProcessor(df)
            print(f"✅ Mock data created successfully: {processor.df.shape}")
            return True, processor.df
    except Exception as e:
        print(f"❌ Data loading error: {e}")
        return False, None

def test_data_processor():
    """Test DataProcessor functionality."""
    try:
        success, df = test_data_loading()
        if not success:
            return False
        
        processor = DataProcessor(df)
        
        # Test filtering
        filtered = processor.filter_data(countries=['CZ'], weeks=df['week'].unique()[:3])
        print(f"✅ Data filtering works: {len(filtered)} records after filtering")
        
        # Test aggregation
        agg_data = processor.aggregate_data(df, ['country'])
        print(f"✅ Data aggregation works: {len(agg_data)} aggregated records")
        
        # Test summary statistics
        summary = processor.create_summary_statistics(df)
        print(f"✅ Summary statistics work: {len(summary)} summary records")
        
        return True
    except Exception as e:
        print(f"❌ DataProcessor error: {e}")
        return False

def test_recommendation_system():
    """Test the recommendation system."""
    try:
        success, df = test_data_loading()
        if not success:
            return False
        
        rec_system = AdvancedRecommendationSystem(df)
        
        selected_weeks = sorted(df['week'].unique())
        results = rec_system.analyze_optimal_periods(
            selected_weeks=selected_weeks,
            analysis_period=3,  # Short period for testing
            target_ratio=100.0,
            countries=['CZ', 'HU', 'SK']
        )
        
        if 'error' not in results:
            print(f"✅ Recommendation system works: {results['summary']['successful_analyses']} successful analyses")
        else:
            print(f"⚠️ Recommendation system limitation: {results['error']}")
        
        return True
    except Exception as e:
        print(f"❌ Recommendation system error: {e}")
        return False

def test_export_functions():
    """Test export functionality."""
    try:
        success, df = test_data_loading()
        if not success:
            return False
        
        # Test CSV export
        csv_data = exporter.export_csv(df.head(10))
        print(f"✅ CSV export works: {len(csv_data)} bytes")
        
        # Test Excel export
        excel_data = exporter.export_excel({'test_data': df.head(10)})
        print(f"✅ Excel export works: {len(excel_data)} bytes")
        
        # Test JSON export
        json_data = exporter.export_json({'test': 'data'})
        print(f"✅ JSON export works: {len(json_data)} bytes")
        
        return True
    except Exception as e:
        print(f"❌ Export functions error: {e}")
        return False

def test_config_files():
    """Test configuration files exist and are readable."""
    config_files = [
        '.streamlit/config.toml',
        'config/theme_config.py',
        'assets/styles.css',
        'assets/animations.css'
    ]
    
    all_exist = True
    for file_path in config_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def run_all_tests():
    """Run all tests and provide summary."""
    print("🧪 Running Enhanced Dashboard Tests")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Data Loading", test_data_loading),
        ("Data Processor", test_data_processor),
        ("Recommendation System", test_recommendation_system),
        ("Export Functions", test_export_functions),
        ("Config Files", test_config_files)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        try:
            if test_name == "Data Loading":
                success, _ = test_func()
            else:
                success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status:<10} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The enhanced dashboard is ready to run.")
        print("   Run with: streamlit run main.py")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    run_all_tests()
"""Enhanced UI components for the Sales and Deliveries Dashboard with dark theme support."""

import streamlit as st
import pandas as pd
import numpy as np
import time
from typing import Dict, List, Any, Optional

def load_css():
    """Load custom CSS styles for the application."""
    try:
        with open('assets/styles.css', 'r') as f:
            styles = f.read()
        with open('assets/animations.css', 'r') as f:
            animations = f.read()
        
        st.markdown(f'<style>{styles}</style>', unsafe_allow_html=True)
        st.markdown(f'<style>{animations}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.warning("Custom CSS files not found. Using default styling.")

def create_animated_header(title: str, subtitle: str = "") -> None:
    """Create an animated header with glassmorphism effect."""
    if subtitle:
        header_html = f"""
        <div class="main-header gradient-animate">
            <h1 style="margin: 0; font-size: 3rem;">{title}</h1>
            <p style="margin: 0.5rem 0 0 0; font-size: 1.2rem; opacity: 0.8;">{subtitle}</p>
        </div>
        """
    else:
        header_html = f'<div class="main-header gradient-animate"><h1 style="margin: 0;">{title}</h1></div>'
    
    st.markdown(header_html, unsafe_allow_html=True)

def create_metric_card(label: str, value: str, delta: Optional[str] = None, 
                      delta_color: str = "normal", icon: str = "📊") -> None:
    """Create an enhanced metric card with glassmorphism and animations."""
    delta_html = ""
    if delta:
        color_map = {
            "normal": "#fafafa",
            "inverse": "#fafafa", 
            "off": "#666"
        }
        delta_html = f'<div style="color: {color_map.get(delta_color, "#fafafa")}; font-size: 0.9rem; margin-top: 0.5rem;">{delta}</div>'
    
    card_html = f"""
    <div class="metric-container scale-hover flip-in">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <div style="color: #00d4aa; font-size: 0.9rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">
                    {label}
                </div>
                <div class="metric-value" style="color: #fafafa; font-size: 2.5rem; font-weight: 700; margin: 0.5rem 0;">
                    {value}
                </div>
                {delta_html}
            </div>
            <div style="font-size: 3rem; opacity: 0.3;">
                {icon}
            </div>
        </div>
    </div>
    """
    st.markdown(card_html, unsafe_allow_html=True)

def create_enhanced_selectbox(label: str, options: List[str], index: int = 0, 
                            key: str = None, help_text: str = None) -> str:
    """Create an enhanced selectbox with better styling."""
    return st.selectbox(
        label,
        options,
        index=index,
        key=key,
        help=help_text
    )

def create_time_period_selector(df: pd.DataFrame, key_prefix: str = "time_") -> Dict[str, Any]:
    """Create an enhanced time period selector with preset options."""
    st.markdown("""
    <div class="week-selector-container">
        <h3 style="color: #00d4aa; margin: 0 0 1rem 0; display: flex; align-items: center;">
            <span style="margin-right: 0.5rem;">📅</span>
            Time Period Selection
        </h3>
        <p style="margin: 0 0 1.5rem 0; color: #fafafa; opacity: 0.8;">
            Choose your analysis time frame with flexible options
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Get available weeks
    all_weeks = sorted(df['week'].unique())
    week_labels = [format_week_label(week) for week in all_weeks]
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown("**Quick Presets:**")
        preset = st.selectbox(
            "Choose preset:",
            ["Custom", "Last 4 weeks", "Last 8 weeks", "Last 12 weeks", "Last 26 weeks", "All time"],
            key=f"{key_prefix}preset"
        )
    
    with col2:
        st.markdown("**Analysis Period:**")
        period_length = st.slider(
            "Weeks to analyze:",
            min_value=1,
            max_value=52,
            value=12,
            key=f"{key_prefix}length"
        )
    
    # Handle preset selections
    if preset == "Last 4 weeks":
        selected_weeks = all_weeks[-4:]
    elif preset == "Last 8 weeks":
        selected_weeks = all_weeks[-8:]
    elif preset == "Last 12 weeks":
        selected_weeks = all_weeks[-12:]
    elif preset == "Last 26 weeks":
        selected_weeks = all_weeks[-26:]
    elif preset == "All time":
        selected_weeks = all_weeks
    else:  # Custom
        col3, col4 = st.columns(2)
        with col3:
            start_idx = st.selectbox(
                "Start week:",
                range(len(week_labels)),
                format_func=lambda x: week_labels[x],
                key=f"{key_prefix}start"
            )
        with col4:
            end_idx = st.selectbox(
                "End week:",
                range(len(week_labels)),
                index=min(start_idx + period_length - 1, len(week_labels) - 1),
                format_func=lambda x: week_labels[x],
                key=f"{key_prefix}end"
            )
        
        if start_idx <= end_idx:
            selected_weeks = all_weeks[start_idx:end_idx + 1]
        else:
            selected_weeks = all_weeks
    
    # Display selection summary
    if selected_weeks:
        st.success(f"✅ **Selected:** {len(selected_weeks)} weeks from {format_week_label(selected_weeks[0])} to {format_week_label(selected_weeks[-1])}")
    
    return {
        'selected_weeks': selected_weeks,
        'period_length': len(selected_weeks),
        'preset': preset
    }

def create_loading_spinner(text: str = "Loading...") -> None:
    """Create an animated loading spinner."""
    spinner_html = f"""
    <div style="text-align: center; padding: 2rem;">
        <div class="loading-spinner"></div>
        <p style="color: #00d4aa; font-weight: 600; margin-top: 1rem;">{text}</p>
    </div>
    """
    st.markdown(spinner_html, unsafe_allow_html=True)

def create_progress_bar(progress: float, text: str = "") -> None:
    """Create an animated progress bar."""
    progress_html = f"""
    <div style="margin: 1rem 0;">
        <div style="color: #fafafa; margin-bottom: 0.5rem; font-weight: 600;">{text}</div>
        <div style="background: rgba(255,255,255,0.1); border-radius: 10px; overflow: hidden;">
            <div class="progress-animated" style="
                --progress-width: {progress}%;
                height: 10px;
                background: linear-gradient(90deg, #00d4aa, #17a2b8);
                border-radius: 10px;
                transition: width 0.5s ease;
            "></div>
        </div>
        <div style="color: #00d4aa; font-size: 0.9rem; margin-top: 0.5rem; font-weight: 600;">
            {progress:.1f}% Complete
        </div>
    </div>
    """
    st.markdown(progress_html, unsafe_allow_html=True)

def create_notification(message: str, type: str = "info", duration: int = 3) -> None:
    """Create an animated notification."""
    colors = {
        "success": "#28a745",
        "warning": "#ffc107", 
        "error": "#dc3545",
        "info": "#17a2b8"
    }
    
    icons = {
        "success": "✅",
        "warning": "⚠️",
        "error": "❌",
        "info": "ℹ️"
    }
    
    color = colors.get(type, colors["info"])
    icon = icons.get(type, icons["info"])
    
    notification_html = f"""
    <div class="notification-enter" style="
        background: rgba({','.join(str(int(color.lstrip('#')[i:i+2], 16)) for i in (0, 2, 4))}, 0.2);
        border: 1px solid {color};
        border-radius: 12px;
        padding: 1rem;
        margin: 1rem 0;
        color: {color};
        font-weight: 600;
        display: flex;
        align-items: center;
    ">
        <span style="margin-right: 0.5rem; font-size: 1.2rem;">{icon}</span>
        {message}
    </div>
    """
    st.markdown(notification_html, unsafe_allow_html=True)
    
    if duration > 0:
        time.sleep(duration)

def create_enhanced_dataframe(df: pd.DataFrame, title: str = "", height: int = 400) -> None:
    """Create an enhanced dataframe display with animations."""
    if title:
        st.markdown(f"""
        <div style="margin: 2rem 0 1rem 0;">
            <h3 style="color: #00d4aa; margin: 0; font-weight: 600;">{title}</h3>
        </div>
        """, unsafe_allow_html=True)
    
    # Apply styling function for ratio columns
    def highlight_ratio_columns(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)
        for col in df.columns:
            if 'Ratio' in col or '%' in col:
                styles[col] = 'font-weight: bold; background-color: rgba(0, 212, 170, 0.2); color: #00d4aa;'
        return styles
    
    styled_df = df.style.apply(highlight_ratio_columns, axis=None)
    st.dataframe(styled_df, use_container_width=True, height=height)

def create_chart_container(chart_function, title: str = "") -> None:
    """Create a container for charts with enhanced styling."""
    if title:
        st.markdown(f"""
        <div style="margin: 2rem 0 1rem 0;">
            <h3 style="color: #00d4aa; margin: 0; font-weight: 600; display: flex; align-items: center;">
                <span style="margin-right: 0.5rem;">📊</span>
                {title}
            </h3>
        </div>
        """, unsafe_allow_html=True)
    
    container = st.container()
    with container:
        chart_function()

def format_week_label(week_str: str) -> str:
    """Format week string for display."""
    if pd.isna(week_str):
        return ""
    try:
        year = week_str[3:5]
        week = week_str[6:8] 
        return f"{year}w{week}"
    except (IndexError, TypeError):
        return str(week_str)

def create_export_section(df: pd.DataFrame, filename_prefix: str = "data") -> None:
    """Create an enhanced export section with multiple format options."""
    st.markdown("""
    <div style="margin: 2rem 0 1rem 0;">
        <h3 style="color: #00d4aa; margin: 0; font-weight: 600; display: flex; align-items: center;">
            <span style="margin-right: 0.5rem;">📥</span>
            Export Data
        </h3>
        <p style="color: #fafafa; opacity: 0.8; margin: 0.5rem 0 1rem 0;">
            Download your data in various formats for further analysis
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        csv_data = df.to_csv(index=False)
        st.download_button(
            label="📊 Download CSV",
            data=csv_data,
            file_name=f"{filename_prefix}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv",
            use_container_width=True
        )
    
    with col2:
        # Excel export would require creating the buffer here
        st.download_button(
            label="📈 Download Excel",
            data=csv_data,  # Simplified for now
            file_name=f"{filename_prefix}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv",
            use_container_width=True
        )
    
    with col3:
        json_data = df.to_json(orient='records', indent=2)
        st.download_button(
            label="🔧 Download JSON",
            data=json_data,
            file_name=f"{filename_prefix}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json",
            use_container_width=True
        )
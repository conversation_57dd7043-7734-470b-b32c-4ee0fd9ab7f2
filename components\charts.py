"""Enhanced chart components for dark theme visualization with Highcharts."""

import streamlit as st
import streamlit_highcharts as hct
import pandas as pd
import numpy as np
from typing import Dict, List, Any
from config.theme_config import COUNTRY_COLORS, TREND_COLORS, CHART_CONFIG

def create_dark_theme_highcharts_config(base_config: Dict) -> Dict:
    """Apply dark theme styling to Highcharts configuration."""
    dark_theme = {
        "chart": {
            "backgroundColor": "rgba(26, 30, 46, 0.8)",
            "style": {
                "fontFamily": "'Segoe UI', Arial, sans-serif",
                "color": "#fafafa"
            }
        },
        "title": {
            "style": {
                "color": "#00d4aa",
                "fontSize": "1.5rem",
                "fontWeight": "600"
            }
        },
        "subtitle": {
            "style": {
                "color": "#fafafa",
                "opacity": "0.8"
            }
        },
        "xAxis": {
            "gridLineColor": "rgba(255, 255, 255, 0.1)",
            "lineColor": "rgba(255, 255, 255, 0.2)",
            "tickColor": "rgba(255, 255, 255, 0.2)",
            "labels": {
                "style": {
                    "color": "#fafafa"
                }
            },
            "title": {
                "style": {
                    "color": "#00d4aa",
                    "fontWeight": "600"
                }
            }
        },
        "yAxis": {
            "gridLineColor": "rgba(255, 255, 255, 0.1)",
            "lineColor": "rgba(255, 255, 255, 0.2)",
            "tickColor": "rgba(255, 255, 255, 0.2)",
            "labels": {
                "style": {
                    "color": "#fafafa"
                }
            },
            "title": {
                "style": {
                    "color": "#00d4aa",
                    "fontWeight": "600"
                }
            }
        },
        "legend": {
            "itemStyle": {
                "color": "#fafafa"
            },
            "itemHoverStyle": {
                "color": "#00d4aa"
            },
            "backgroundColor": "rgba(38, 39, 48, 0.8)",
            "borderColor": "rgba(255, 255, 255, 0.1)",
            "borderRadius": "8px",
            "shadow": False
        },
        "tooltip": {
            "backgroundColor": "rgba(38, 39, 48, 0.95)",
            "borderColor": "rgba(0, 212, 170, 0.3)",
            "borderRadius": "8px",
            "borderWidth": 1,
            "shadow": False,
            "style": {
                "color": "#fafafa",
                "fontSize": "12px"
            }
        },
        "plotOptions": {
            "series": {
                "animation": {
                    "duration": 1000,
                    "easing": "easeOutCubic"
                }
            },
            "column": {
                "borderWidth": 0,
                "borderRadius": "4px",
                "states": {
                    "hover": {
                        "brightness": 0.1,
                        "shadow": {
                            "color": "rgba(0, 212, 170, 0.3)",
                            "width": 10,
                            "offsetX": 0,
                            "offsetY": 0
                        }
                    }
                }
            },
            "spline": {
                "states": {
                    "hover": {
                        "lineWidthPlus": 1,
                        "halo": {
                            "size": 10,
                            "attributes": {
                                "fill": "rgba(0, 212, 170, 0.2)"
                            }
                        }
                    }
                }
            }
        }
    }
    
    # Deep merge the dark theme into base config
    def deep_merge(base: Dict, overlay: Dict) -> Dict:
        result = base.copy()
        for key, value in overlay.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = deep_merge(result[key], value)
            else:
                result[key] = value
        return result
    
    return deep_merge(base_config, dark_theme)

def create_enhanced_combined_chart(chart_data: pd.DataFrame, countries: List[str], 
                                 filtered_weeks: List[str], title: str = "Sales and Deliveries Analysis") -> Dict:
    """Create an enhanced combined chart with dark theme and animations."""
    
    # Parse custom week format for sorting
    def parse_custom_week(week_str):
        if pd.isna(week_str):
            return 0
        year = int(week_str[1:5])
        week = int(week_str[6:8])
        return year * 100 + week
    
    def format_week_label(week_str):
        if pd.isna(week_str):
            return ""
        year = week_str[3:5]
        week = week_str[6:8]
        return f"{year}w{week}"
    
    def add_trendline(x, y):
        """Calculate trendline for the data."""
        if len(x) < 2 or len(y) < 2:
            return [0] * len(x)
        
        from scipy import stats
        x_numeric = x.apply(parse_custom_week)
        
        try:
            valid_indices = ~(pd.isna(x_numeric) | pd.isna(y))
            if valid_indices.sum() < 2:
                return [0] * len(x)
            
            x_clean = x_numeric[valid_indices]
            y_clean = y[valid_indices]
            
            slope, intercept, _, _, _ = stats.linregress(x_clean, y_clean)
            line = slope * x_numeric + intercept
            return line.fillna(0).tolist()
        except Exception:
            return [0] * len(x)
    
    # Get all weeks for consistent x-axis
    all_weeks_in_range = sorted(filtered_weeks, key=parse_custom_week)
    week_labels = [format_week_label(w) for w in all_weeks_in_range]
    
    # Prepare series data for all countries
    series_data = []
    y_axes = []
    
    for i, country in enumerate(countries):
        country_data = chart_data[chart_data['country'] == country].copy()
        country_data = country_data.sort_values('week', key=lambda x: x.apply(parse_custom_week))
        
        # Create complete dataset with all weeks (fill missing weeks with 0)
        week_to_sold = dict(zip(country_data['week'], country_data['sold_units']))
        week_to_delivered = dict(zip(country_data['week'], country_data['unit_delivered']))
        
        sold_units = [week_to_sold.get(week, 0) for week in all_weeks_in_range]
        delivered_units = [week_to_delivered.get(week, 0) for week in all_weeks_in_range]
        
        # Calculate trend lines
        if len(country_data) > 1:
            sold_trend = add_trendline(country_data['week'], country_data['sold_units'])
            delivered_trend = add_trendline(country_data['week'], country_data['unit_delivered'])
        else:
            sold_trend = [0] * len(all_weeks_in_range)
            delivered_trend = [0] * len(all_weeks_in_range)
        
        country_colors = COUNTRY_COLORS.get(country, ['#1f77b4', '#aec7e8'])
        
        # Create Y-axis for this country
        y_axes.append({
            "title": {
                "text": f"{country} - Units",
                "style": {"color": "#00d4aa", "fontWeight": "600"}
            },
            "top": f"{i * 33.33}%",
            "height": "30%",
            "offset": 0,
            "min": 0,
            "gridLineColor": "rgba(255, 255, 255, 0.1)",
            "lineColor": "rgba(255, 255, 255, 0.2)",
            "tickColor": "rgba(255, 255, 255, 0.2)",
            "labels": {
                "style": {"color": "#fafafa"},
                "formatter": "function() { return Highcharts.numberFormat(this.value, 0, '', ','); }"
            }
        })
        
        # Add series for this country with enhanced styling
        series_data.extend([
            {
                "type": "column",
                "name": f"{country} - Sold Units",
                "data": sold_units,
                "color": {
                    "linearGradient": {"x1": 0, "y1": 0, "x2": 0, "y2": 1},
                    "stops": [
                        [0, country_colors[0]],
                        [1, f"{country_colors[0]}80"]
                    ]
                },
                "yAxis": i,
                "borderRadius": "4px",
                "shadow": {
                    "color": f"{country_colors[0]}40",
                    "offsetX": 0,
                    "offsetY": 2,
                    "opacity": 0.3,
                    "width": 3
                }
            },
            {
                "type": "column",
                "name": f"{country} - Units Delivered",
                "data": delivered_units,
                "color": {
                    "linearGradient": {"x1": 0, "y1": 0, "x2": 0, "y2": 1},
                    "stops": [
                        [0, country_colors[1]],
                        [1, f"{country_colors[1]}80"]
                    ]
                },
                "yAxis": i,
                "borderRadius": "4px",
                "shadow": {
                    "color": f"{country_colors[1]}40",
                    "offsetX": 0,
                    "offsetY": 2,
                    "opacity": 0.3,
                    "width": 3
                }
            },
            {
                "type": "spline",
                "name": f"{country} - Sold Trend",
                "data": sold_trend,
                "color": TREND_COLORS['sold'],
                "lineWidth": 3,
                "marker": {
                    "enabled": False,
                    "states": {
                        "hover": {
                            "enabled": True,
                            "fillColor": TREND_COLORS['sold'],
                            "lineColor": "#ffffff",
                            "lineWidth": 2,
                            "radius": 6
                        }
                    }
                },
                "yAxis": i,
                "shadow": {
                    "color": f"{TREND_COLORS['sold']}60",
                    "offsetX": 0,
                    "offsetY": 1,
                    "opacity": 0.4,
                    "width": 2
                }
            },
            {
                "type": "spline",
                "name": f"{country} - Delivered Trend",
                "data": delivered_trend,
                "color": TREND_COLORS['delivered'],
                "lineWidth": 3,
                "marker": {
                    "enabled": False,
                    "states": {
                        "hover": {
                            "enabled": True,
                            "fillColor": TREND_COLORS['delivered'],
                            "lineColor": "#ffffff",
                            "lineWidth": 2,
                            "radius": 6
                        }
                    }
                },
                "yAxis": i,
                "shadow": {
                    "color": f"{TREND_COLORS['delivered']}60",
                    "offsetX": 0,
                    "offsetY": 1,
                    "opacity": 0.4,
                    "width": 2
                }
            }
        ])
    
    base_config = {
        "title": {
            "text": title,
            "align": "center",
            "style": {
                "fontSize": "1.8rem",
                "fontWeight": "700",
                "color": "#00d4aa"
            }
        },
        "subtitle": {
            "text": f"Analysis across {len(countries)} countries for {len(week_labels)} weeks",
            "align": "center",
            "style": {
                "fontSize": "1rem",
                "color": "#fafafa",
                "opacity": "0.8"
            }
        },
        "xAxis": {
            "categories": week_labels,
            "title": {
                "text": "Week",
                "style": {"color": "#00d4aa", "fontWeight": "600"}
            },
            "labels": {
                "rotation": -45,
                "style": {"color": "#fafafa"}
            },
            "crosshair": {
                "width": 1,
                "color": "rgba(0, 212, 170, 0.3)"
            }
        },
        "yAxis": y_axes,
        "legend": {
            "align": "right",
            "verticalAlign": "top",
            "layout": "vertical",
            "x": -10,
            "y": 50,
            "floating": True,
            "backgroundColor": "rgba(38, 39, 48, 0.9)",
            "borderColor": "rgba(255, 255, 255, 0.1)",
            "borderRadius": "8px",
            "borderWidth": 1,
            "shadow": False,
            "itemStyle": {"color": "#fafafa"},
            "itemHoverStyle": {"color": "#00d4aa"}
        },
        "plotOptions": {
            "column": {
                "grouping": True,
                "shadow": True,
                "borderWidth": 0,
                "animation": {
                    "duration": 1000,
                    "easing": "easeOutBounce"
                },
                "states": {
                    "hover": {
                        "brightness": 0.1,
                        "shadow": True
                    }
                }
            },
            "spline": {
                "animation": {
                    "duration": 1500,
                    "easing": "easeOutCubic"
                },
                "states": {
                    "hover": {
                        "lineWidthPlus": 2,
                        "halo": {
                            "size": 10,
                            "attributes": {
                                "fill": "rgba(0, 212, 170, 0.2)",
                                "stroke": "rgba(0, 212, 170, 0.4)",
                                "strokeWidth": 1
                            }
                        }
                    }
                }
            }
        },
        "series": series_data,
        "tooltip": {
            "shared": True,
            "crosshairs": True,
            "backgroundColor": "rgba(38, 39, 48, 0.95)",
            "borderColor": "rgba(0, 212, 170, 0.5)",
            "borderRadius": "8px",
            "borderWidth": 1,
            "shadow": False,
            "style": {
                "color": "#fafafa",
                "fontSize": "13px",
                "fontWeight": "500"
            },
            "headerFormat": '<div style="font-weight: 700; color: #00d4aa; margin-bottom: 8px;">📅 {point.x}</div>',
            "pointFormat": '''
                <div style="margin: 4px 0; padding: 4px 8px; border-radius: 4px; background: rgba(0,0,0,0.2);">
                    <span style="color: {series.color}; font-weight: 600;">●</span>
                    <span style="margin-left: 8px;">{series.name}</span>
                    <span style="float: right; font-weight: 700; color: #00d4aa; margin-left: 16px;">
                        {point.y:,.0f}
                    </span>
                </div>
            ''',
            "footerFormat": '<div style="margin-top: 8px; font-size: 11px; opacity: 0.7; text-align: center;">Click and drag to zoom</div>',
            "useHTML": True
        },
        "credits": {
            "enabled": False
        },
        "exporting": {
            "enabled": True,
            "buttons": {
                "contextButton": {
                    "theme": {
                        "fill": "rgba(38, 39, 48, 0.9)",
                        "stroke": "rgba(255, 255, 255, 0.1)",
                        "style": {"color": "#fafafa"}
                    }
                }
            }
        }
    }
    
    return create_dark_theme_highcharts_config(base_config)

def create_scatter_chart(df: pd.DataFrame, title: str = "Sold vs Delivered Units Correlation") -> Dict:
    """Create an enhanced scatter plot for correlation analysis."""
    
    # Prepare data for scatter plot
    scatter_data = []
    
    for country in df['country'].unique():
        country_data = df[df['country'] == country]
        country_colors = COUNTRY_COLORS.get(country, ['#1f77b4'])
        
        for _, row in country_data.iterrows():
            scatter_data.append({
                "x": row['sold_units'],
                "y": row['unit_delivered'],
                "name": f"{country} - Week {row.get('week', '')}",
                "country": country,
                "color": country_colors[0]
            })
    
    base_config = {
        "chart": {
            "type": "scatter",
            "zoomType": "xy"
        },
        "title": {
            "text": title
        },
        "subtitle": {
            "text": "Each point represents a week of data. Drag to zoom in/out."
        },
        "xAxis": {
            "title": {"text": "Sold Units"},
            "startOnTick": True,
            "endOnTick": True,
            "showLastLabel": True
        },
        "yAxis": {
            "title": {"text": "Delivered Units"}
        },
        "plotOptions": {
            "scatter": {
                "marker": {
                    "radius": 8,
                    "symbol": "circle",
                    "states": {
                        "hover": {
                            "enabled": True,
                            "lineColor": "#ffffff",
                            "lineWidth": 2,
                            "radius": 12
                        }
                    }
                },
                "states": {
                    "hover": {
                        "marker": {
                            "enabled": False
                        }
                    }
                }
            }
        },
        "series": [
            {
                "name": country,
                "data": [{"x": point["x"], "y": point["y"], "name": point["name"]} 
                        for point in scatter_data if point["country"] == country],
                "color": COUNTRY_COLORS.get(country, ['#1f77b4'])[0]
            } 
            for country in df['country'].unique()
        ],
        "tooltip": {
            "headerFormat": "<b>{point.name}</b><br>",
            "pointFormat": "Sold: {point.x:,.0f}<br>Delivered: {point.y:,.0f}"
        }
    }
    
    return create_dark_theme_highcharts_config(base_config)

def render_enhanced_chart(chart_config: Dict, height: int = 600, key: str = None) -> None:
    """Render an enhanced chart with animations and dark theme."""
    try:
        hct.streamlit_highcharts(chart_config, height=height, key=key)
    except Exception as e:
        st.error(f"Error rendering chart: {str(e)}")
        st.json(chart_config)  # For debugging
import streamlit as st
import pandas as pd
import numpy as np
from scipy import stats
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import io
from datetime import datetime

# Page configuration
st.set_page_config(
    page_title="Sales and Deliveries Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-container {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .sidebar .sidebar-content {
        background-color: #f8f9fa;
    }
    /* Enhanced table styling for delivery ratios */
    .stDataFrame table td:nth-child(n):contains("Ratio") {
        font-weight: bold !important;
        background-color: #e8f4f8 !important;
        color: #1f77b4 !important;
    }
    .stDataFrame table th:nth-child(n):contains("Ratio") {
        font-weight: bold !important;
        background-color: #d1ecf1 !important;
        color: #0c5460 !important;
    }
    /* Week selector styling */
    .week-selector-container {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_data():
    """Load and cache the parquet data"""
    try:
        df = pd.read_parquet("24w26_25w26.parquet")
        return df
    except FileNotFoundError:
        st.error("Data file '24w26_25w26.parquet' not found. Please ensure the file is in the correct directory.")
        return None

def parse_custom_week(week_str):
    """Parse custom week format to numeric value for sorting"""
    if pd.isna(week_str):
        return 0
    year = int(week_str[1:5])
    week = int(week_str[6:8])
    return year * 100 + week

def format_week_label(week_str):
    """Format week string for display"""
    if pd.isna(week_str):
        return ""
    # Convert f2024w26 to 24w26
    year = week_str[3:5]  # Get last 2 digits of year
    week = week_str[6:8]  # Get week number
    return f"{year}w{week}"

def format_large_number(num):
    """Format large numbers with K/M suffixes"""
    if pd.isna(num):
        return "0"
    if num >= 1_000_000:
        return f"{num/1_000_000:.1f}M"
    elif num >= 1_000:
        return f"{num/1_000:.1f}K"
    else:
        return f"{num:.0f}"

def create_advanced_week_selector(df):
    """Create advanced week selection component at top of sidebar"""

    st.sidebar.markdown("### 📅 Week Selection")
    st.sidebar.markdown("*Applies to both Country and Department Analysis*")

    # Get all available weeks
    all_weeks = sorted(df['week'].unique(), key=parse_custom_week)

    # Create formatted week options for display
    week_options = [format_week_label(week) for week in all_weeks]
    week_to_original = dict(zip(week_options, all_weeks))

    # Range selector with formatted labels
    col1, col2 = st.sidebar.columns(2)

    with col1:
        start_week = st.selectbox(
            "Start Week:",
            week_options,
            index=0,
            key="start_week_selector"
        )

    with col2:
        end_week = st.selectbox(
            "End Week:",
            week_options,
            index=len(week_options)-1,
            key="end_week_selector"
        )

    # Get range of weeks
    start_idx = week_options.index(start_week)
    end_idx = week_options.index(end_week)

    if start_idx <= end_idx:
        selected_weeks = [week_to_original[week_options[i]] for i in range(start_idx, end_idx + 1)]
    else:
        st.sidebar.error("Start week must be before or equal to end week")
        selected_weeks = all_weeks

    # Display selection summary
    if selected_weeks:
        st.sidebar.markdown(f"**Selected:** {len(selected_weeks)} weeks")
        st.sidebar.markdown(f"**Range:** {format_week_label(selected_weeks[0])} to {format_week_label(selected_weeks[-1])}")

    st.sidebar.markdown('</div>', unsafe_allow_html=True)
    st.sidebar.markdown("---")

    return selected_weeks if selected_weeks else all_weeks

def create_excel_download(table_data, is_pmg_level):
    """Create Excel file for download"""

    # Create Excel buffer
    excel_buffer = io.BytesIO()

    # Create Excel writer
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        # Write main data
        sheet_name = "PMG_Analysis" if is_pmg_level else "Department_Analysis"
        table_data.to_excel(writer, sheet_name=sheet_name, index=False)

        # Get worksheet to apply formatting
        worksheet = writer.sheets[sheet_name]

        # Apply formatting to ratio columns
        try:
            from openpyxl.styles import Font, PatternFill

            # Find ratio columns
            ratio_cols = []
            for idx, col in enumerate(table_data.columns, 1):
                if 'Ratio %' in col:
                    ratio_cols.append(idx)

            # Apply formatting to ratio columns
            blue_fill = PatternFill(start_color="E8F4F8", end_color="E8F4F8", fill_type="solid")
            bold_font = Font(bold=True, color="1F77B4")

            for col_idx in ratio_cols:
                for row_idx in range(2, len(table_data) + 2):  # Skip header row
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell.fill = blue_fill
                    cell.font = bold_font

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        except ImportError:
            # If openpyxl styling is not available, just save without formatting
            pass

    excel_buffer.seek(0)
    return excel_buffer.getvalue()

@st.cache_data
def create_recommendation_system(filtered_df, selected_weeks, required_period_length=12):
    """Create recommendation system to find optimal periods closest to 100% delivery ratio"""

    with st.spinner(f"🔍 Analyzing optimal {required_period_length}-week periods across all countries..."):
        # Calculate weekly ratios for each department and country
        weekly_data = filtered_df.groupby(['dep name', 'country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()
        weekly_data['delivery_ratio'] = (weekly_data['unit_delivered'] / weekly_data['sold_units'] * 100).round(2)

        # Get all departments
        departments = sorted(weekly_data['dep name'].unique())

        recommendations = []

        for dept in departments:
            dept_data = weekly_data[weekly_data['dep name'] == dept].copy()

            # Find the optimal period for this department
            best_period = find_optimal_12week_period(dept_data, selected_weeks, required_period_length)

            if best_period:
                # Calculate distance from 100% for display
                distance_from_100 = abs(100 - best_period['overall_avg'])
                
                # Calculate individual country distances from 100%
                cz_distance = abs(100 - best_period['avg_ratios']['CZ'])
                hu_distance = abs(100 - best_period['avg_ratios']['HU'])
                sk_distance = abs(100 - best_period['avg_ratios']['SK'])

                recommendations.append({
                    'Department': dept,
                    'Optimal Period Start': format_week_label(best_period['start_week']),
                    'Optimal Period End': format_week_label(best_period['end_week']),
                    'Period Length': f'{required_period_length} weeks',
                    'CZ Ratio': f"{best_period['avg_ratios']['CZ']:.1f}%",
                    'CZ Distance': f"{cz_distance:.1f}%",
                    'HU Ratio': f"{best_period['avg_ratios']['HU']:.1f}%",
                    'HU Distance': f"{hu_distance:.1f}%",
                    'SK Ratio': f"{best_period['avg_ratios']['SK']:.1f}%",
                    'SK Distance': f"{sk_distance:.1f}%",
                    'Overall Average': f"{best_period['overall_avg']:.1f}%",
                    'Overall Distance': f"{distance_from_100:.1f}%",
                    'Optimization Score': f"{best_period['score']:.2f}"
                })

        if recommendations:
            recommendations_df = pd.DataFrame(recommendations)

            # Sort by optimization score (best optimization first - lower score is better)
            recommendations_df['_sort_score'] = recommendations_df['Optimization Score'].astype(float)
            recommendations_df = recommendations_df.sort_values('_sort_score').drop('_sort_score', axis=1)

            return {
                'recommendations_df': recommendations_df,
                'recommendations': recommendations,
                'period_length': required_period_length
            }
        else:
            return {
                'recommendations_df': None,
                'recommendations': [],
                'period_length': required_period_length,
                'error': f"No sufficient {required_period_length}-week periods found for recommendation analysis. Try selecting a longer time range (at least {required_period_length} weeks)."
            }

def display_recommendation_results(results_data, period_length):
    """Display recommendation results with Excel download functionality"""
    
    if 'error' in results_data:
        st.warning(f"⚠️ {results_data['error']}")
        return
    
    recommendations_df = results_data['recommendations_df']
    recommendations = results_data['recommendations']
    
    if recommendations_df is not None and len(recommendations) > 0:
        # Apply styling to ratio and distance columns
        def highlight_ratio_columns(df):
            styles = pd.DataFrame('', index=df.index, columns=df.columns)

            ratio_columns = ['CZ Ratio', 'HU Ratio', 'SK Ratio', 'Overall Average']
            distance_columns = ['CZ Distance', 'HU Distance', 'SK Distance', 'Overall Distance']
            
            for col in ratio_columns:
                if col in df.columns:
                    styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'
            
            for col in distance_columns:
                if col in df.columns:
                    styles[col] = 'font-weight: bold; background-color: #fff2cc; color: #d6b656;'

            return styles

        styled_recommendations = recommendations_df.style.apply(highlight_ratio_columns, axis=None)
        st.dataframe(styled_recommendations, use_container_width=True, height=400, hide_index=True)

        # Excel download button
        excel_buffer = create_excel_download(recommendations_df, is_pmg_level=False)
        st.download_button(
            label="📥 Download Recommendations as Excel",
            data=excel_buffer,
            file_name=f"optimal_periods_recommendations_{period_length}weeks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

        # Find department with best optimization score (lowest score is best)
        best_dept = min(recommendations, key=lambda x: float(x['Optimization Score']))
        st.success(f"🎯 **Most Optimal Performance:** {best_dept['Department']} during {best_dept['Optimal Period Start']} to {best_dept['Optimal Period End']} with {best_dept['Overall Average']} average delivery ratio (only {best_dept['Overall Distance']} away from perfect 100%)")

        # Show analysis summary
        st.info(f"📊 **Analysis Summary:** Found optimal {period_length}-week periods for {len(recommendations)} departments. Periods are ranked by proximity to 100% delivery ratio across all countries (CZ, HU, SK).")

def find_optimal_12week_period(dept_data, selected_weeks, required_period_length=12):
    """Find the optimal consecutive period closest to 100% delivery ratio across all countries"""

    countries = ['CZ', 'HU', 'SK']

    # Check if we have enough weeks for analysis
    if len(selected_weeks) < required_period_length:
        return None

    best_period = None
    best_score = float('inf')  # Lower score is better (closest to 100%)

    # Try all possible consecutive periods
    for start_idx in range(len(selected_weeks) - required_period_length + 1):
        end_idx = start_idx + required_period_length - 1
        period_weeks = selected_weeks[start_idx:end_idx + 1]

        # Validate that we have exactly the required number of weeks
        if len(period_weeks) != required_period_length:
            continue

        # Calculate average ratios for this period
        period_ratios = {}
        valid_countries = 0

        # Check data completeness for each country
        for country in countries:
            country_data = dept_data[
                (dept_data['country'] == country) &
                (dept_data['week'].isin(period_weeks))
            ]

            # Ensure we have data for all weeks for this country
            if len(country_data) == required_period_length:
                avg_ratio = country_data['delivery_ratio'].mean()
                if not pd.isna(avg_ratio) and avg_ratio > 0:
                    period_ratios[country] = avg_ratio
                    valid_countries += 1

        # Only consider periods where ALL countries have COMPLETE data for all weeks
        if valid_countries == len(countries):
            overall_avg = np.mean(list(period_ratios.values()))
            ratio_values = list(period_ratios.values())

            # NEW SCORING: Focus on individual country performance
            # Calculate distance from 100% for each country individually
            individual_distances = [abs(100 - ratio) for ratio in ratio_values]
            
            # Primary score: Sum of individual country distances (lower is better)
            # This ensures each country is considered equally important
            individual_score = sum(individual_distances)
            
            # Secondary penalty: Standard deviation (penalize unbalanced performance)
            std_dev = np.std(ratio_values)
            balance_penalty = std_dev * 0.5  # Increased penalty for unbalanced performance
            
            # Tertiary penalty: Extreme values for any individual country
            extreme_penalty = 0
            for ratio in ratio_values:
                if ratio < 50 or ratio > 150:
                    extreme_penalty += 100  # Heavy penalty for any country with unrealistic ratios
                elif ratio < 70 or ratio > 130:
                    extreme_penalty += 25   # Medium penalty for moderately extreme ratios
            
            # Final score combines all factors
            final_score = individual_score + balance_penalty + extreme_penalty

            if final_score < best_score:
                best_score = final_score
                best_period = {
                    'start_week': period_weeks[0],
                    'end_week': period_weeks[-1],
                    'length': required_period_length,
                    'avg_ratios': period_ratios,
                    'overall_avg': overall_avg,
                    'score': final_score,
                    'individual_distances': individual_distances,
                    'balance_score': std_dev
                }

    return best_period

def find_best_consecutive_period(dept_data, selected_weeks):
    """Legacy function - kept for compatibility"""
    return find_optimal_12week_period(dept_data, selected_weeks)

def add_trendline(x, y):
    """Calculate trendline for the data - returns x and y values for the trendline"""
    if len(x) < 2 or len(y) < 2:
        return x, [0] * len(x)

    x_numeric = x.apply(parse_custom_week)
    try:
        # Remove any NaN values
        valid_indices = ~(pd.isna(x_numeric) | pd.isna(y))
        if valid_indices.sum() < 2:
            return x, [0] * len(x)

        x_clean = x_numeric[valid_indices]
        y_clean = y[valid_indices]

        slope, intercept, _, _, _ = stats.linregress(x_clean, y_clean)
        line = slope * x_numeric + intercept
        return x, line.fillna(0).tolist()
    except Exception as e:
        return x, [0] * len(x)

def create_plotly_chart(chart_data, countries, selected_weeks):
    """Create Plotly chart with subplots for all countries"""
    
    # Color palette for countries
    colors = {
        'CZ': ['#4e79a7', '#a0cbe8'], 
        'HU': ['#59a14f', '#8cd17d'], 
        'SK': ['#9c755f', '#c9b18f']
    }
    
    # Create subplots
    fig = make_subplots(
        rows=len(countries), cols=1,
        shared_xaxes=True,
        vertical_spacing=0.02,
        subplot_titles=["" for _ in countries]
    )
    
    for i, country in enumerate(countries, start=1):
        country_data = chart_data[chart_data['country'] == country].copy()
        
        # Format week labels
        week_labels = country_data["week"].apply(format_week_label)
        
        # Add bar charts
        fig.add_trace(
            go.Bar(
                x=week_labels, 
                y=country_data["sold_units"],
                name="Sold Units", 
                marker_color=colors[country][0],
                showlegend=(i==1), 
                opacity=0.8,
                hovertemplate="<b>Sold Units</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>",
                customdata=[format_large_number(x) for x in country_data["sold_units"]],
                offset=-0.2, 
                width=0.4
            ),
            row=i, col=1
        )
        
        fig.add_trace(
            go.Bar(
                x=week_labels, 
                y=country_data["unit_delivered"],
                name="Units Delivered", 
                marker_color=colors[country][1],
                showlegend=(i==1), 
                opacity=0.8,
                hovertemplate="<b>Units Delivered</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>",
                customdata=[format_large_number(x) for x in country_data["unit_delivered"]],
                offset=0.2, 
                width=0.4
            ),
            row=i, col=1
        )
        
        # Add trendlines - exactly 2 lines per chart
        for column, color, name in [("sold_units", "#FFA500", "Sold Units Trend"), 
                                    ("unit_delivered", "#FF4136", "Units Delivered Trend")]:
            x_trend, y_trend = add_trendline(country_data["week"], country_data[column])
            fig.add_trace(
                go.Scatter(
                    x=x_trend.apply(format_week_label), 
                    y=y_trend, 
                    mode='lines', 
                    name=name,
                    line=dict(color=color, width=2, dash='solid'), 
                    showlegend=(i==1),
                    hovertemplate=f"<b>{name}</b><br>Week: %{{x}}<br>Units: %{{customdata}}<extra></extra>",
                    customdata=[format_large_number(x) for x in y_trend]
                ),
                row=i, col=1
            )
    
    # Update layout for dark theme
    fig.update_layout(
        title_text="<b>Sales and Deliveries Analysis</b>",
        title_font=dict(size=24, color='white'),
        barmode='overlay',
        bargap=0,
        bargroupgap=0,
        legend=dict(
            orientation="h", 
            yanchor="bottom", 
            y=1.02, 
            xanchor="right", 
            x=1,
            font=dict(color='white')
        ),
        showlegend=True,
        margin=dict(l=100, r=100, t=100, b=50),
        plot_bgcolor='#1E1E1E',
        paper_bgcolor='#121212',
        font=dict(family="Arial, sans-serif", size=12, color="white"),
        hoverlabel=dict(bgcolor="white", font_size=12, font_color="black"),
        height=800
    )
    
    # Update axes for dark theme
    fig.update_xaxes(
        title_text="",
        tickangle=45,
        gridcolor='#333333',
        tickfont=dict(size=12, color='white'),
        title_font=dict(size=12, color='white')
    )
    
    fig.update_yaxes(
        title_text="",
        gridcolor='#333333',
        tickformat=',d',
        title_font=dict(size=10, color='white'),
        tickfont=dict(size=12, color='white')
    )
    
    # Remove x-axis titles for all but the last subplot
    for i in range(1, len(countries)):
        fig.update_xaxes(title_text="", row=i, col=1)
    
    # Add country labels and styling for dark theme
    for i, country in enumerate(countries, start=1):
        fig.update_xaxes(showline=True, linewidth=1, linecolor='#444444', mirror=True, row=i, col=1)
        fig.update_yaxes(showline=True, linewidth=1, linecolor='#444444', mirror=True, row=i, col=1)
        
        fig.add_annotation(
            text=f"<b>{country}</b>",
            xref="paper", yref="paper",
            x=1.02, y=1 - (i-1)/len(countries) - 0.5/len(countries),
            showarrow=False,
            font=dict(size=14, color='white'),
            xanchor="left", yanchor="middle"
        )
    
    return fig

def main():
    # Main header
    st.markdown('<h1 class="main-header">📊 Sales and Deliveries Dashboard</h1>', unsafe_allow_html=True)

    # Load data
    df = load_data()
    if df is None:
        return

    # Advanced week selection at top of sidebar (applies to both tabs)
    selected_weeks = create_advanced_week_selector(df)

    # Create tabs
    tab1, tab2 = st.tabs(["🌍 Country Analysis", "🏢 Department Analysis"])

    with tab1:
        country_analysis_tab(df, selected_weeks)

    with tab2:
        department_analysis_tab(df, selected_weeks)

def country_analysis_tab(df, selected_weeks):
    # Add country analysis specific filters
    st.sidebar.header("🔧 Country Analysis Filters")
    
    # Department filter
    departments = ['Total'] + sorted(df['dep name'].dropna().unique().tolist())
    selected_department = st.sidebar.selectbox(
        "Select Department:",
        departments,
        index=0
    )

    # Add filter summary
    st.sidebar.markdown("---")


    # Filter data based on selections
    filtered_df = df[df['week'].isin(selected_weeks)].copy()
    
    if selected_department != 'Total':
        filtered_df = filtered_df[filtered_df['dep name'] == selected_department]
    
    # Main content area
    if len(filtered_df) == 0:
        st.warning("No data available for the selected filters.")
        return
    
    # Summary metrics
    st.subheader("📈 Summary Metrics")
    col1, col2, col3 = st.columns(3)
    
    total_sold = filtered_df['sold_units'].sum()
    total_delivered = filtered_df['unit_delivered'].sum()
    
    with col1:
        st.metric(
            label="Total Sold Units",
            value=format_large_number(total_sold),
            delta=None
        )
    
    with col2:
        st.metric(
            label="Total Delivered Units", 
            value=format_large_number(total_delivered),
            delta=None
        )
    
    with col3:
        delivery_ratio = (total_delivered / total_sold * 100) if total_sold > 0 else 0
        st.metric(
            label="Delivery Ratio",
            value=f"{delivery_ratio:.1f}%",
            delta=None
        )
    
    # Chart section

    # Aggregate data for charting
    if selected_department == 'Total':
        chart_data = filtered_df[filtered_df['dep'] != 'UNA'].groupby(['country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()
    else:
        chart_data = filtered_df.groupby(['country', 'week'])[['sold_units', 'unit_delivered']].sum().reset_index()

    # Create combined chart with subplots for all countries
    countries = sorted(chart_data['country'].unique())

    if len(countries) > 0:
        # Create Plotly chart
        fig = create_plotly_chart(chart_data, countries, selected_weeks)
        st.plotly_chart(fig, use_container_width=True)



def department_analysis_tab(df, selected_weeks):
    """Department analysis tab with table view of department metrics"""

    # Add department analysis specific filters
    st.sidebar.header("🔧 Department Analysis Filters")

    # PMG breakdown toggle
    show_pmg_breakdown = st.sidebar.checkbox(
        "Show PMG Level",
        value=False,
        help="When enabled, shows PMG-level breakdown by country instead of department level"
    )

    # Filter data by week range (show all departments)
    filtered_df = df[df['week'].isin(selected_weeks)].copy()

    if len(filtered_df) == 0:
        st.warning("No data available for the selected filters.")
        return

    # Create and display the table
    if show_pmg_breakdown:
        st.subheader("📊 All Departments - PMG Level Breakdown")
        table_data = create_all_pmg_table_by_country(filtered_df)
    else:
        st.subheader("📊 All Departments Summary")
        table_data = create_all_departments_table_by_country(filtered_df)

    # Excel download button
    if table_data is not None:
        excel_buffer = create_excel_download(table_data, show_pmg_breakdown)
        st.download_button(
            label="📥 Download as Excel",
            data=excel_buffer,
            file_name=f"department_analysis_{'pmg' if show_pmg_breakdown else 'dept'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

    # Recommendation System
    st.subheader("🎯 Recommendation System")
    st.markdown("**Find optimal periods where delivery ratios are closest to 100% across all countries**")

    # Initialize session state for recommendations
    if 'recommendations_data' not in st.session_state:
        st.session_state.recommendations_data = None
    if 'recommendations_period_length' not in st.session_state:
        st.session_state.recommendations_period_length = 12

    # Use form to avoid automatic recalculation
    with st.form("recommendation_form"):

        col_1, col_2 = st.columns(2)

        with col_1:
            # Period length input
            required_period_length = st.number_input(
                "Period Length (weeks):",
                min_value=1,
                max_value=52,
                value=st.session_state.recommendations_period_length,
                step=1,
                help="Number of consecutive weeks to analyze for optimal periods"
            )
        with col_2:
            st.write("")  # Spacer
            submitted = st.form_submit_button("🔍 Analyze Optimal Periods", use_container_width=True)

        if submitted:
            # Store the analysis parameters and results in session state
            st.session_state.recommendations_period_length = required_period_length
            st.session_state.recommendations_data = create_recommendation_system(filtered_df, selected_weeks, required_period_length)

    # Display existing recommendations if available
    if st.session_state.recommendations_data is not None:
        display_recommendation_results(st.session_state.recommendations_data, st.session_state.recommendations_period_length)

def create_all_departments_table_by_country(filtered_df):
    """Create table showing all departments with country breakdown"""

    # Group by department and country
    dept_summary = filtered_df.groupby(['dep name', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
    dept_summary['delivery_ratio'] = (dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100).round(1)

    # Pivot to show countries as columns
    sold_pivot = dept_summary.pivot(index='dep name', columns='country', values='sold_units').fillna(0)
    delivered_pivot = dept_summary.pivot(index='dep name', columns='country', values='unit_delivered').fillna(0)
    ratio_pivot = dept_summary.pivot(index='dep name', columns='country', values='delivery_ratio').fillna(0)

    # Create combined table
    combined_data = []
    for dept in sold_pivot.index:
        row = {'Department': dept}
        for country in ['CZ', 'HU', 'SK']:
            if country in sold_pivot.columns:
                sold = sold_pivot.loc[dept, country]
                delivered = delivered_pivot.loc[dept, country]
                ratio = ratio_pivot.loc[dept, country]

                row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
        combined_data.append(row)

    result_df = pd.DataFrame(combined_data)

    # Apply styling to delivery ratio columns
    def highlight_ratio_columns(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)

        # Apply styling to ratio columns
        for col in df.columns:
            if 'Ratio %' in col:
                styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'

        return styles

    styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
    st.dataframe(styled_df, use_container_width=True, height=500, hide_index=True)

    return result_df

def create_all_pmg_table_by_country(filtered_df):
    """Create table showing all PMGs with country breakdown"""

    # Check if PMG column exists
    if 'pmg_name_total' not in filtered_df.columns:
        st.warning("PMG data not available in the dataset.")
        return

    # Group by department, PMG and country
    pmg_summary = filtered_df.groupby(['dep name', 'pmg_name_total', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
    pmg_summary['delivery_ratio'] = (pmg_summary['unit_delivered'] / pmg_summary['sold_units'] * 100).round(1)

    # Pivot to show countries as columns
    sold_pivot = pmg_summary.pivot_table(index=['dep name', 'pmg_name_total'], columns='country', values='sold_units', fill_value=0)
    delivered_pivot = pmg_summary.pivot_table(index=['dep name', 'pmg_name_total'], columns='country', values='unit_delivered', fill_value=0)
    ratio_pivot = pmg_summary.pivot_table(index=['dep name', 'pmg_name_total'], columns='country', values='delivery_ratio', fill_value=0)

    # Create combined table
    combined_data = []
    for (dept, pmg) in sold_pivot.index:
        row = {'Department': dept, 'PMG Name': pmg}
        for country in ['CZ', 'HU', 'SK']:
            if country in sold_pivot.columns:
                sold = sold_pivot.loc[(dept, pmg), country]
                delivered = delivered_pivot.loc[(dept, pmg), country]
                ratio = ratio_pivot.loc[(dept, pmg), country]

                row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
        combined_data.append(row)

    result_df = pd.DataFrame(combined_data)

    # Apply styling to delivery ratio columns
    def highlight_ratio_columns(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)

        # Apply styling to ratio columns
        for col in df.columns:
            if 'Ratio %' in col:
                styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'

        return styles

    styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
    st.dataframe(styled_df, use_container_width=True, height=600, hide_index=True)

    return result_df

def create_department_table_by_country(filtered_df):
    """Create department-level summary table by country"""

    # Group by country
    dept_summary = filtered_df.groupby('country')[['sold_units', 'unit_delivered']].sum().reset_index()
    dept_summary['delivery_ratio'] = (dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100).round(1)

    # Format numbers
    dept_summary['sold_units_formatted'] = dept_summary['sold_units'].apply(lambda x: f"{x:,.0f}")
    dept_summary['delivered_units_formatted'] = dept_summary['unit_delivered'].apply(lambda x: f"{x:,.0f}")
    dept_summary['ratio_formatted'] = dept_summary['delivery_ratio'].apply(lambda x: f"{x:.1f}%")

    # Display table with styling
    display_df = dept_summary[['country', 'sold_units_formatted', 'delivered_units_formatted', 'ratio_formatted']].copy()
    display_df.columns = ['Country', 'Sold Units', 'Delivered Units', 'Delivery Ratio']

    # Apply styling to delivery ratio column
    def highlight_ratio_column(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)
        styles['Delivery Ratio'] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'
        return styles

    styled_df = display_df.style.apply(highlight_ratio_column, axis=None)
    st.dataframe(styled_df, use_container_width=True, hide_index=True)

def create_pmg_table_by_country(filtered_df):
    """Create PMG-level summary table by country"""

    # Check if PMG column exists
    if 'pmg_name_total' not in filtered_df.columns:
        st.warning("PMG data not available in the dataset.")
        return

    # Group by PMG and country
    pmg_summary = filtered_df.groupby(['pmg_name_total', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
    pmg_summary['delivery_ratio'] = (pmg_summary['unit_delivered'] / pmg_summary['sold_units'] * 100).round(1)

    # Pivot to show countries as columns
    sold_pivot = pmg_summary.pivot(index='pmg_name_total', columns='country', values='sold_units').fillna(0)
    delivered_pivot = pmg_summary.pivot(index='pmg_name_total', columns='country', values='unit_delivered').fillna(0)
    ratio_pivot = pmg_summary.pivot(index='pmg_name_total', columns='country', values='delivery_ratio').fillna(0)

    # Create combined table
    combined_data = []
    for pmg in sold_pivot.index:
        row = {'PMG Name': pmg}
        for country in ['CZ', 'HU', 'SK']:
            if country in sold_pivot.columns:
                sold = sold_pivot.loc[pmg, country]
                delivered = delivered_pivot.loc[pmg, country]
                ratio = ratio_pivot.loc[pmg, country]

                row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
        combined_data.append(row)

    result_df = pd.DataFrame(combined_data)

    # Apply styling to delivery ratio columns
    def highlight_ratio_columns(df):
        styles = pd.DataFrame('', index=df.index, columns=df.columns)

        # Apply styling to ratio columns
        for col in df.columns:
            if 'Ratio %' in col:
                styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'

        return styles

    styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
    st.dataframe(styled_df, use_container_width=True, height=400, hide_index=True)

def create_department_table(filtered_df, show_all_departments):
    """Create department-level summary table"""

    if show_all_departments:
        # Group by department and country
        dept_summary = filtered_df.groupby(['dep name', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()

        # Calculate delivery ratio
        dept_summary['delivery_ratio'] = (dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100).round(1)

        # Pivot to show countries as columns
        sold_pivot = dept_summary.pivot(index='dep name', columns='country', values='sold_units').fillna(0)
        delivered_pivot = dept_summary.pivot(index='dep name', columns='country', values='unit_delivered').fillna(0)
        ratio_pivot = dept_summary.pivot(index='dep name', columns='country', values='delivery_ratio').fillna(0)

        # Create combined table
        combined_data = []
        for dept in sold_pivot.index:
            row = {'Department': dept}
            for country in ['CZ', 'HU', 'SK']:
                if country in sold_pivot.columns:
                    sold = sold_pivot.loc[dept, country]
                    delivered = delivered_pivot.loc[dept, country]
                    ratio = ratio_pivot.loc[dept, country]

                    row[f'{country} - Sold Units'] = f"{sold:,.0f}"
                    row[f'{country} - Delivered Units'] = f"{delivered:,.0f}"
                    row[f'{country} - Ratio %'] = f"{ratio:.1f}%"
            combined_data.append(row)

        result_df = pd.DataFrame(combined_data)

        # Apply styling to delivery ratio columns
        def highlight_ratio_columns(df):
            # Create a style dataframe with same shape as result_df
            styles = pd.DataFrame('', index=df.index, columns=df.columns)

            # Apply styling to ratio columns
            for col in df.columns:
                if 'Ratio %' in col:
                    styles[col] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'

            return styles

        styled_df = result_df.style.apply(highlight_ratio_columns, axis=None)
        st.dataframe(styled_df, use_container_width=True, height=400, hide_index=True)

    else:
        # Single department view by country
        dept_summary = filtered_df.groupby('country')[['sold_units', 'unit_delivered']].sum().reset_index()
        dept_summary['delivery_ratio'] = (dept_summary['unit_delivered'] / dept_summary['sold_units'] * 100).round(1)

        # Format numbers
        dept_summary['sold_units_formatted'] = dept_summary['sold_units'].apply(lambda x: f"{x:,.0f}")
        dept_summary['delivered_units_formatted'] = dept_summary['unit_delivered'].apply(lambda x: f"{x:,.0f}")
        dept_summary['ratio_formatted'] = dept_summary['delivery_ratio'].apply(lambda x: f"{x:.1f}%")

        # Display table with styling
        display_df = dept_summary[['country', 'sold_units_formatted', 'delivered_units_formatted', 'ratio_formatted']].copy()
        display_df.columns = ['Country', 'Sold Units', 'Delivered Units', 'Delivery Ratio']

        # Apply styling to delivery ratio column
        def highlight_ratio_column(df):
            styles = pd.DataFrame('', index=df.index, columns=df.columns)
            styles['Delivery Ratio'] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'
            return styles

        styled_df = display_df.style.apply(highlight_ratio_column, axis=None)
        st.dataframe(styled_df, use_container_width=True, hide_index=True)

def create_pmg_table(filtered_df, show_all_departments):
    """Create PMG-level summary table"""

    # Check if PMG column exists
    if 'pmg_name_total' not in filtered_df.columns:
        st.warning("PMG data not available in the dataset.")
        return

    if show_all_departments:
        # Group by department, PMG, and country
        pmg_summary = filtered_df.groupby(['dep name', 'pmg_name_total', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()

        # Calculate delivery ratio
        pmg_summary['delivery_ratio'] = (pmg_summary['unit_delivered'] / pmg_summary['sold_units'] * 100).round(1)

        # Create summary by department and PMG
        pmg_dept_summary = pmg_summary.groupby(['dep name', 'pmg_name_total'])[['sold_units', 'unit_delivered']].sum().reset_index()
        pmg_dept_summary['delivery_ratio'] = (pmg_dept_summary['unit_delivered'] / pmg_dept_summary['sold_units'] * 100).round(1)

        # Format numbers
        pmg_dept_summary['sold_units_formatted'] = pmg_dept_summary['sold_units'].apply(lambda x: f"{x:,.0f}")
        pmg_dept_summary['delivered_units_formatted'] = pmg_dept_summary['unit_delivered'].apply(lambda x: f"{x:,.0f}")
        pmg_dept_summary['ratio_formatted'] = pmg_dept_summary['delivery_ratio'].apply(lambda x: f"{x:.1f}%")

        # Display table with styling
        display_df = pmg_dept_summary[['dep name', 'pmg_name_total', 'sold_units_formatted', 'delivered_units_formatted', 'ratio_formatted']].copy()
        display_df.columns = ['Department', 'PMG Name', 'Sold Units', 'Delivered Units', 'Delivery Ratio']

        # Apply styling to delivery ratio column
        def highlight_ratio_column(df):
            styles = pd.DataFrame('', index=df.index, columns=df.columns)
            styles['Delivery Ratio'] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'
            return styles

        styled_df = display_df.style.apply(highlight_ratio_column, axis=None)
        st.dataframe(styled_df, use_container_width=True, height=500, hide_index=True)

    else:
        # Single department PMG breakdown
        pmg_summary = filtered_df.groupby(['pmg_name_total', 'country'])[['sold_units', 'unit_delivered']].sum().reset_index()
        pmg_summary['delivery_ratio'] = (pmg_summary['unit_delivered'] / pmg_summary['sold_units'] * 100).round(1)

        # Summary by PMG only
        pmg_total_summary = pmg_summary.groupby('pmg_name_total')[['sold_units', 'unit_delivered']].sum().reset_index()
        pmg_total_summary['delivery_ratio'] = (pmg_total_summary['unit_delivered'] / pmg_total_summary['sold_units'] * 100).round(1)

        # Format numbers
        pmg_total_summary['sold_units_formatted'] = pmg_total_summary['sold_units'].apply(lambda x: f"{x:,.0f}")
        pmg_total_summary['delivered_units_formatted'] = pmg_total_summary['unit_delivered'].apply(lambda x: f"{x:,.0f}")
        pmg_total_summary['ratio_formatted'] = pmg_total_summary['delivery_ratio'].apply(lambda x: f"{x:.1f}%")

        # Display table with styling
        display_df = pmg_total_summary[['pmg_name_total', 'sold_units_formatted', 'delivered_units_formatted', 'ratio_formatted']].copy()
        display_df.columns = ['PMG Name', 'Sold Units', 'Delivered Units', 'Delivery Ratio']

        # Apply styling to delivery ratio column
        def highlight_ratio_column(df):
            styles = pd.DataFrame('', index=df.index, columns=df.columns)
            styles['Delivery Ratio'] = 'font-weight: bold; background-color: #e8f4f8; color: #1f77b4;'
            return styles

        styled_df = display_df.style.apply(highlight_ratio_column, axis=None)
        st.dataframe(styled_df, use_container_width=True, height=400, hide_index=True)



def create_highcharts_config(country, weeks, sold_units, delivered_units, sold_trend, delivered_trend):
    """Create Highcharts configuration"""

    # Color scheme based on country
    colors = {
        'CZ': ['#4e79a7', '#a0cbe8'],
        'HU': ['#59a14f', '#8cd17d'],
        'SK': ['#9c755f', '#c9b18f']
    }

    country_colors = colors.get(country, ['#1f77b4', '#aec7e8'])

    # Ensure all data lists have the same length
    data_length = len(weeks)
    sold_units = sold_units[:data_length] + [0] * max(0, data_length - len(sold_units))
    delivered_units = delivered_units[:data_length] + [0] * max(0, data_length - len(delivered_units))
    sold_trend = sold_trend[:data_length] + [0] * max(0, data_length - len(sold_trend))
    delivered_trend = delivered_trend[:data_length] + [0] * max(0, data_length - len(delivered_trend))

    config = {
        "title": {
            "text": f"Sales and Deliveries - {country}",
            "style": {"fontSize": "18px", "fontWeight": "bold"}
        },
        "xAxis": {
            "categories": weeks,
            "title": {"text": "Week"},
            "labels": {"rotation": -45}
        },
        "yAxis": {
            "title": {"text": "Units"},
            "min": 0
        },
        "legend": {
            "align": "center",
            "verticalAlign": "top",
            "layout": "horizontal"
        },
        "plotOptions": {
            "column": {
                "grouping": True,
                "shadow": False,
                "borderWidth": 0,
                "states": {
                    "hover": {
                        "brightness": 0.1,
                        "shadow": False
                    }
                }
            },
            "spline": {
                "states": {
                    "hover": {
                        "lineWidthPlus": 1
                    }
                }
            }
        },
        "series": [
            {
                "type": "column",
                "name": "Sold Units",
                "data": sold_units,
                "color": country_colors[0],
                "opacity": 0.8
            },
            {
                "type": "column",
                "name": "Units Delivered",
                "data": delivered_units,
                "color": country_colors[1],
                "opacity": 0.8
            },
            {
                "type": "spline",
                "name": "Sold Units Trend",
                "data": sold_trend,
                "color": "#FFA500",
                "lineWidth": 2,
                "marker": {"enabled": False}
            },
            {
                "type": "spline",
                "name": "Units Delivered Trend",
                "data": delivered_trend,
                "color": "#FF4136",
                "lineWidth": 2,
                "marker": {"enabled": False}
            }
        ],
        "tooltip": {
            "enabled": True,
            "shared": True,
            "crosshairs": True,
            "backgroundColor": "white",
            "borderColor": "#ccc",
            "borderRadius": 3,
            "shadow": False,
            "style": {
                "fontSize": "12px"
            },
            "pointFormat": "<span style='color:{series.color}'>{series.name}</span>: <b>{point.y:,.0f}</b><br/>",
            "headerFormat": "<b>{point.x}</b><br/>"
        }
    }

    return config

def create_combined_chart_config(chart_data, countries, filtered_weeks):
    """Create combined chart configuration with subplots for all countries"""

    # Color scheme for countries
    colors = {
        'CZ': ['#4e79a7', '#a0cbe8'],
        'HU': ['#59a14f', '#8cd17d'],
        'SK': ['#9c755f', '#c9b18f']
    }

    # Get all weeks for consistent x-axis
    all_weeks_in_range = sorted(filtered_weeks, key=parse_custom_week)
    week_labels = [format_week_label(w) for w in all_weeks_in_range]

    # Prepare series data for all countries
    series_data = []

    for i, country in enumerate(countries):
        country_data = chart_data[chart_data['country'] == country].copy()
        country_data = country_data.sort_values('week', key=lambda x: x.apply(parse_custom_week))

        # Create a complete dataset with all weeks (fill missing weeks with 0)
        week_to_sold = dict(zip(country_data['week'], country_data['sold_units']))
        week_to_delivered = dict(zip(country_data['week'], country_data['unit_delivered']))

        sold_units = [week_to_sold.get(week, 0) for week in all_weeks_in_range]
        delivered_units = [week_to_delivered.get(week, 0) for week in all_weeks_in_range]

        # Calculate trend lines
        if len(country_data) > 1:
            _, sold_trend = add_trendline(country_data['week'], country_data['sold_units'])
            _, delivered_trend = add_trendline(country_data['week'], country_data['unit_delivered'])
        else:
            sold_trend = [0] * len(all_weeks_in_range)
            delivered_trend = [0] * len(all_weeks_in_range)

        country_colors = colors.get(country, ['#1f77b4', '#aec7e8'])

        # Add series for this country
        series_data.extend([
            {
                "type": "column",
                "name": f"{country} - Sold Units",
                "data": sold_units,
                "color": country_colors[0],
                "opacity": 0.8,
                "yAxis": i
            },
            {
                "type": "column",
                "name": f"{country} - Units Delivered",
                "data": delivered_units,
                "color": country_colors[1],
                "opacity": 0.8,
                "yAxis": i
            },
            {
                "type": "spline",
                "name": f"{country} - Sold Trend",
                "data": sold_trend,
                "color": "#FFA500",
                "lineWidth": 2,
                "marker": {"enabled": False},
                "yAxis": i
            },
            {
                "type": "spline",
                "name": f"{country} - Delivered Trend",
                "data": delivered_trend,
                "color": "#FF4136",
                "lineWidth": 2,
                "marker": {"enabled": False},
                "yAxis": i
            }
        ])

    # Create y-axes for each country
    y_axes = []
    for i, country in enumerate(countries):
        y_axes.append({
            "title": {"text": f"{country} - Units"},
            "top": f"{i * 33.33}%",
            "height": "30%",
            "offset": 0,
            "min": 0
        })

    config = {
        "title": {
            "text": "Sales and Deliveries - All Countries",
            "style": {"fontSize": "20px", "fontWeight": "bold"}
        },
        "xAxis": {
            "categories": week_labels,
            "title": {"text": "Week"},
            "labels": {"rotation": -45}
        },
        "yAxis": y_axes,
        "legend": {
            "align": "right",
            "verticalAlign": "top",
            "layout": "vertical",
            "x": -10,
            "y": 100
        },
        "plotOptions": {
            "column": {
                "grouping": True,
                "shadow": False,
                "borderWidth": 0,
                "states": {
                    "hover": {
                        "brightness": 0.1,
                        "shadow": False
                    }
                }
            },
            "spline": {
                "states": {
                    "hover": {
                        "lineWidthPlus": 1
                    }
                }
            }
        },
        "series": series_data,
        "tooltip": {
            "enabled": True,
            "shared": True,
            "crosshairs": True,
            "backgroundColor": "white",
            "borderColor": "#ccc",
            "borderRadius": 3,
            "shadow": False,
            "style": {
                "fontSize": "12px"
            },
            "pointFormat": "<span style='color:{series.color}'>{series.name}</span>: <b>{point.y:,.0f}</b><br/>",
            "headerFormat": "<b>{point.x}</b><br/>"
        }
    }

    return config



def create_pmg_chart_config(pmg_data, pmgs, filtered_weeks):
    """Create chart configuration for PMG breakdown"""

    # Format weeks for display
    formatted_weeks = [format_week_label(week) for week in filtered_weeks]

    # Prepare series data for each PMG
    series_data = []

    # Color palette for PMGs
    colors = ['#4e79a7', '#f28e2c', '#e15759', '#76b7b2', '#59a14f', '#edc949',
              '#af7aa1', '#ff9d9a', '#9c755f', '#bab0ab']

    for i, pmg in enumerate(pmgs[:10]):  # Limit to top 10 PMGs for readability
        pmg_subset = pmg_data[pmg_data['pmg_name_total'] == pmg].copy()
        pmg_subset = pmg_subset.sort_values('week', key=lambda x: x.apply(parse_custom_week))

        # Create complete dataset with all weeks
        week_to_sold = dict(zip(pmg_subset['week'], pmg_subset['sold_units']))
        week_to_delivered = dict(zip(pmg_subset['week'], pmg_subset['unit_delivered']))

        sold_units = [week_to_sold.get(week, 0) for week in filtered_weeks]
        delivered_units = [week_to_delivered.get(week, 0) for week in filtered_weeks]

        # Add sold units series
        series_data.append({
            "type": "column",
            "name": f"{pmg} - Sold Units",
            "data": sold_units,
            "color": colors[i % len(colors)],
            "yAxis": 0
        })

        # Add delivered units series
        series_data.append({
            "type": "column",
            "name": f"{pmg} - Delivered Units",
            "data": delivered_units,
            "color": colors[i % len(colors)],
            "opacity": 0.7,
            "yAxis": 0
        })

    config = {
        "title": {"text": "PMG Breakdown - Sold vs Delivered Units"},
        "xAxis": {"categories": formatted_weeks},
        "yAxis": {
            "title": {"text": "Units"},
            "labels": {"formatter": "function() { return Highcharts.numberFormat(this.value, 0); }"}
        },
        "legend": {
            "align": "right",
            "verticalAlign": "top",
            "layout": "vertical"
        },
        "plotOptions": {
            "column": {
                "grouping": True,
                "shadow": False,
                "borderWidth": 0,
                "states": {
                    "hover": {
                        "brightness": 0.1,
                        "shadow": False
                    }
                }
            }
        },
        "tooltip": {
            "enabled": True,
            "shared": True,
            "crosshairs": True,
            "backgroundColor": "white",
            "borderColor": "#ccc",
            "borderRadius": 3,
            "shadow": False,
            "style": {
                "fontSize": "12px"
            },
            "pointFormat": "<span style='color:{series.color}'>{series.name}</span>: <b>{point.y:,.0f}</b><br/>",
            "headerFormat": "<b>{point.x}</b><br/>"
        },
        "series": series_data
    }

    return config

if __name__ == "__main__":
    main()

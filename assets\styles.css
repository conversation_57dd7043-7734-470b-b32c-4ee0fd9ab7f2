/* Modern Dark Theme Styles for Sales and Deliveries Dashboard */

/* Global styles */
.main-container {
    font-family: 'Segoe UI', 'Arial', sans-serif;
}

/* Header styling with glassmorphism effect */
.main-header {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #00d4aa 0%, #17a2b8 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    margin: 2rem 0;
    padding: 1rem;
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(0, 212, 170, 0.3); }
    to { box-shadow: 0 0 30px rgba(0, 212, 170, 0.5); }
}

/* Enhanced metric containers with glassmorphism */
.metric-container {
    background: rgba(26, 30, 46, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    margin: 1rem 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 212, 170, 0.2);
    border-color: rgba(0, 212, 170, 0.3);
}

.metric-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left 0.5s;
}

.metric-container:hover::before {
    left: 100%;
}

/* Sidebar enhancements */
.sidebar-container {
    background: rgba(14, 17, 23, 0.95);
    backdrop-filter: blur(15px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Week selector with enhanced styling */
.week-selector-container {
    background: rgba(38, 39, 48, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 170, 0.3);
    border-radius: 16px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.week-selector-container:hover {
    border-color: rgba(0, 212, 170, 0.5);
    transform: translateY(-1px);
}

/* Enhanced table styling */
.stDataFrame {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.stDataFrame table {
    background: rgba(26, 30, 46, 0.8) !important;
    backdrop-filter: blur(10px);
}

.stDataFrame table th {
    background: rgba(38, 39, 48, 0.9) !important;
    color: #00d4aa !important;
    font-weight: 600 !important;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    border-bottom: 2px solid rgba(0, 212, 170, 0.3) !important;
}

.stDataFrame table td {
    background: rgba(26, 30, 46, 0.6) !important;
    color: #fafafa !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    transition: background-color 0.2s ease;
}

.stDataFrame table tr:hover td {
    background: rgba(0, 212, 170, 0.1) !important;
}

/* Ratio column highlighting */
.stDataFrame table td:nth-child(n):contains("Ratio"),
.stDataFrame table th:nth-child(n):contains("Ratio") {
    font-weight: bold !important;
    background: rgba(0, 212, 170, 0.2) !important;
    color: #00d4aa !important;
    border-left: 3px solid #00d4aa !important;
}

/* Button enhancements */
.stButton button {
    background: linear-gradient(135deg, #00d4aa 0%, #17a2b8 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 212, 170, 0.3);
    position: relative;
    overflow: hidden;
}

.stButton button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 212, 170, 0.4);
}

.stButton button:active {
    transform: translateY(0);
}

/* Download button special styling */
.stDownloadButton button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 12px;
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
}

.stDownloadButton button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(40, 167, 69, 0.4);
}

/* Form enhancements */
.stForm {
    background: rgba(26, 30, 46, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Selectbox and input enhancements */
.stSelectbox > div > div {
    background: rgba(38, 39, 48, 0.9) !important;
    border: 1px solid rgba(0, 212, 170, 0.3) !important;
    border-radius: 8px !important;
    color: #fafafa !important;
}

.stSelectbox > div > div:focus {
    border-color: #00d4aa !important;
    box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2) !important;
}

/* Tab styling */
.stTabs > div > div > div {
    background: rgba(26, 30, 46, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px 16px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stTabs > div > div > div > button {
    background: transparent;
    border: none;
    color: #fafafa;
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 12px 12px 0 0;
    transition: all 0.3s ease;
}

.stTabs > div > div > div > button:hover {
    background: rgba(0, 212, 170, 0.1);
    color: #00d4aa;
}

.stTabs > div > div > div > button[aria-selected="true"] {
    background: linear-gradient(135deg, #00d4aa 0%, #17a2b8 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(0, 212, 170, 0.3);
}

/* Success/Warning/Error message styling */
.stSuccess {
    background: rgba(40, 167, 69, 0.2) !important;
    border: 1px solid rgba(40, 167, 69, 0.5) !important;
    border-radius: 12px !important;
    color: #28a745 !important;
}

.stWarning {
    background: rgba(255, 193, 7, 0.2) !important;
    border: 1px solid rgba(255, 193, 7, 0.5) !important;
    border-radius: 12px !important;
    color: #ffc107 !important;
}

.stError {
    background: rgba(220, 53, 69, 0.2) !important;
    border: 1px solid rgba(220, 53, 69, 0.5) !important;
    border-radius: 12px !important;
    color: #dc3545 !important;
}

.stInfo {
    background: rgba(23, 162, 184, 0.2) !important;
    border: 1px solid rgba(23, 162, 184, 0.5) !important;
    border-radius: 12px !important;
    color: #17a2b8 !important;
}

/* Loading spinner animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    border: 4px solid rgba(0, 212, 170, 0.3);
    border-top: 4px solid #00d4aa;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

/* Metric value animation */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.metric-value {
    animation: countUp 0.6s ease-out;
}

/* Chart container styling */
.chart-container {
    background: rgba(26, 30, 46, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.chart-container:hover {
    border-color: rgba(0, 212, 170, 0.3);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
    .main-header {
        font-size: 2rem;
        padding: 0.5rem;
    }
    
    .metric-container {
        padding: 1rem;
        margin: 0.5rem 0;
    }
    
    .week-selector-container {
        padding: 1rem;
    }
}
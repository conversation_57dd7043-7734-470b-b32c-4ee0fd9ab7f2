/* Advanced Animations for Sales and Deliveries Dashboard */

/* Fade-in animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInScale {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Pulse animation for important elements */
@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

/* Bounce animation for success states */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
    40%, 43% { transform: translate3d(0, -10px, 0); }
    70% { transform: translate3d(0, -5px, 0); }
    90% { transform: translate3d(0, -2px, 0); }
}

/* Shimmer effect for loading states */
@keyframes shimmer {
    0% { background-position: -468px 0; }
    100% { background-position: 468px 0; }
}

.shimmer {
    background: linear-gradient(to right, #1a1e2e 4%, #262730 25%, #1a1e2e 36%);
    background-size: 1000px 100%;
    animation: shimmer 2s infinite linear;
}

/* Gradient animation for headers */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.gradient-animate {
    background: linear-gradient(45deg, #00d4aa, #17a2b8, #007bff, #00d4aa);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
}

/* Floating animation for decorative elements */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

/* Scale animation for hover effects */
@keyframes scaleUp {
    from { transform: scale(1); }
    to { transform: scale(1.02); }
}

.scale-hover:hover {
    animation: scaleUp 0.2s ease-out forwards;
}

/* Ripple effect for buttons */
@keyframes ripple {
    0% { transform: scale(0); opacity: 1; }
    20% { transform: scale(25); opacity: 0.377; }
    100% { transform: scale(40); opacity: 0; }
}

.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active:before {
    width: 300px;
    height: 300px;
}

/* Typing animation for text reveals */
@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    from, to { border-color: transparent; }
    50% { border-color: #00d4aa; }
}

.typewriter {
    overflow: hidden;
    border-right: 2px solid #00d4aa;
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink 0.75s step-end infinite;
}

/* Progress bar animation */
@keyframes progressFill {
    from { width: 0%; }
    to { width: var(--progress-width, 100%); }
}

.progress-animated {
    animation: progressFill 2s ease-out forwards;
}

/* Notification slide-in */
@keyframes slideInDown {
    from { transform: translateY(-100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideOutUp {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(-100%); opacity: 0; }
}

.notification-enter {
    animation: slideInDown 0.5s ease-out;
}

.notification-exit {
    animation: slideOutUp 0.5s ease-in;
}

/* Card flip animation for metrics */
@keyframes flipIn {
    from { transform: perspective(400px) rotateY(90deg); opacity: 0; }
    to { transform: perspective(400px) rotateY(0deg); opacity: 1; }
}

.flip-in {
    animation: flipIn 0.6s ease-out;
}

/* Glow effect for important elements */
@keyframes glow {
    from { box-shadow: 0 0 5px rgba(0, 212, 170, 0.2); }
    to { box-shadow: 0 0 20px rgba(0, 212, 170, 0.6); }
}

.glow-effect {
    animation: glow 2s ease-in-out infinite alternate;
}

/* Stagger animation for lists */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.6s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }

/* Particle animation for background */
@keyframes particles {
    0% { transform: translateY(0) rotate(0deg); opacity: 1; }
    100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
}

.particle {
    position: absolute;
    background: rgba(0, 212, 170, 0.1);
    border-radius: 50%;
    animation: particles 10s linear infinite;
}

/* Morphing animation for shapes */
@keyframes morph {
    0% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
    50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
    100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
}

.morph-shape {
    animation: morph 8s ease-in-out infinite;
}

/* Matrix rain effect */
@keyframes matrix {
    0% { transform: translateY(-100%); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(100vh); opacity: 0; }
}

.matrix-rain {
    position: fixed;
    top: 0;
    color: rgba(0, 212, 170, 0.1);
    font-family: monospace;
    font-size: 12px;
    z-index: -1;
    animation: matrix 3s linear infinite;
}

/* Apply animations to common elements */
.stMetric {
    animation: fadeInScale 0.6s ease-out;
}

.stDataFrame {
    animation: fadeIn 0.8s ease-out;
}

.chart-container {
    animation: slideInRight 0.7s ease-out;
}

.sidebar-container {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive animation adjustments */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
"""Enhanced export functionality with multiple format support."""

import pandas as pd
import streamlit as st
import io
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import zipfile
import base64

class EnhancedExporter:
    """Enhanced exporter with multiple format support and styling."""
    
    def __init__(self):
        self.export_formats = {
            'csv': self.export_csv,
            'excel': self.export_excel,
            'json': self.export_json,
            'pdf': self.export_pdf_report,
            'zip': self.export_zip_bundle
        }
    
    def export_csv(self, df: pd.DataFrame, filename: str = None) -> bytes:
        """Export data to CSV format."""
        if filename is None:
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False)
        return csv_buffer.getvalue().encode('utf-8')
    
    def export_excel(self, data: Dict[str, pd.DataFrame], filename: str = None,
                    apply_styling: bool = True) -> bytes:
        """Export data to Excel with multiple sheets and styling."""
        if filename is None:
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        excel_buffer = io.BytesIO()
        
        try:
            with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                for sheet_name, df in data.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    if apply_styling:
                        self._apply_excel_styling(writer, sheet_name, df)
            
        except Exception as e:
            st.error(f"Error creating Excel file: {str(e)}")
            # Fallback to simple Excel without styling
            with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                for sheet_name, df in data.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        return excel_buffer.getvalue()
    
    def _apply_excel_styling(self, writer, sheet_name: str, df: pd.DataFrame) -> None:
        """Apply styling to Excel worksheet."""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            
            worksheet = writer.sheets[sheet_name]
            
            # Header styling
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="1F77B4", end_color="1F77B4", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            # Apply header styling
            for col_idx, column in enumerate(df.columns, 1):
                cell = worksheet.cell(row=1, column=col_idx)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # Ratio column styling
            ratio_fill = PatternFill(start_color="E8F4F8", end_color="E8F4F8", fill_type="solid")
            ratio_font = Font(bold=True, color="1F77B4")
            
            for col_idx, column in enumerate(df.columns, 1):
                if 'Ratio' in str(column) or '%' in str(column):
                    for row_idx in range(2, len(df) + 2):
                        cell = worksheet.cell(row=row_idx, column=col_idx)
                        cell.fill = ratio_fill
                        cell.font = ratio_font
            
            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
                
        except ImportError:
            # openpyxl styling not available
            pass
        except Exception as e:
            st.warning(f"Could not apply Excel styling: {str(e)}")
    
    def export_json(self, data: Dict[str, Any], filename: str = None,
                   include_metadata: bool = True) -> bytes:
        """Export data to JSON format with metadata."""
        if filename is None:
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = data.copy()
        
        if include_metadata:
            export_data['metadata'] = {
                'export_timestamp': datetime.now().isoformat(),
                'export_format': 'json',
                'generated_by': 'Sales and Deliveries Dashboard'
            }
        
        json_str = json.dumps(export_data, indent=2, default=str, ensure_ascii=False)
        return json_str.encode('utf-8')
    
    def export_pdf_report(self, data: Dict[str, Any], filename: str = None) -> bytes:
        """Export a comprehensive PDF report."""
        if filename is None:
            filename = f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        # For now, create a text-based report that can be saved as TXT
        # In a full implementation, you would use a PDF library like reportlab
        
        report_content = self._generate_text_report(data)
        return report_content.encode('utf-8')
    
    def _generate_text_report(self, data: Dict[str, Any]) -> str:
        """Generate a text-based report."""
        report = []
        report.append("SALES AND DELIVERIES DASHBOARD - ANALYSIS REPORT")
        report.append("=" * 60)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Add summary information
        if 'summary' in data:
            summary = data['summary']
            report.append("ANALYSIS SUMMARY")
            report.append("-" * 20)
            for key, value in summary.items():
                report.append(f"{key.replace('_', ' ').title()}: {value}")
            report.append("")
        
        # Add data overview
        if 'data_overview' in data:
            overview = data['data_overview']
            report.append("DATA OVERVIEW")
            report.append("-" * 15)
            for key, value in overview.items():
                if isinstance(value, dict):
                    report.append(f"{key.replace('_', ' ').title()}:")
                    for sub_key, sub_value in value.items():
                        report.append(f"  {sub_key}: {sub_value}")
                else:
                    report.append(f"{key.replace('_', ' ').title()}: {value}")
            report.append("")
        
        # Add recommendations if available
        if 'recommendations' in data and data['recommendations']:
            report.append("TOP RECOMMENDATIONS")
            report.append("-" * 20)
            for i, rec in enumerate(data['recommendations'][:5], 1):  # Top 5
                report.append(f"{i}. {rec.get('Department', 'N/A')}")
                report.append(f"   Period: {rec.get('Optimal Period Start', 'N/A')} to {rec.get('Optimal Period End', 'N/A')}")
                report.append(f"   Overall Average: {rec.get('Overall Average', 'N/A')}")
                report.append(f"   Distance from Target: {rec.get('Distance from Target', 'N/A')}")
                report.append("")
        
        report.append("END OF REPORT")
        
        return "\n".join(report)
    
    def export_zip_bundle(self, data_dict: Dict[str, pd.DataFrame], 
                         metadata: Dict[str, Any] = None, filename: str = None) -> bytes:
        """Export multiple files as a ZIP bundle."""
        if filename is None:
            filename = f"data_bundle_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
        
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Add CSV files
            for name, df in data_dict.items():
                csv_data = self.export_csv(df, f"{name}.csv")
                zip_file.writestr(f"{name}.csv", csv_data)
            
            # Add Excel file with all sheets
            if len(data_dict) > 1:
                excel_data = self.export_excel(data_dict, "combined_data.xlsx")
                zip_file.writestr("combined_data.xlsx", excel_data)
            
            # Add metadata file
            if metadata:
                metadata_json = self.export_json(metadata, "metadata.json")
                zip_file.writestr("metadata.json", metadata_json)
            
            # Add README
            readme_content = self._generate_readme(data_dict, metadata)
            zip_file.writestr("README.txt", readme_content.encode('utf-8'))
        
        return zip_buffer.getvalue()
    
    def _generate_readme(self, data_dict: Dict[str, pd.DataFrame], 
                        metadata: Dict[str, Any] = None) -> str:
        """Generate README content for ZIP bundle."""
        readme = []
        readme.append("Sales and Deliveries Dashboard - Data Export")
        readme.append("=" * 50)
        readme.append(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        readme.append("")
        
        readme.append("FILES INCLUDED:")
        readme.append("-" * 15)
        
        for name, df in data_dict.items():
            readme.append(f"• {name}.csv - {len(df)} records")
        
        if len(data_dict) > 1:
            readme.append("• combined_data.xlsx - All data in Excel format with multiple sheets")
        
        if metadata:
            readme.append("• metadata.json - Export metadata and parameters")
        
        readme.append("• README.txt - This file")
        readme.append("")
        
        readme.append("DATA STRUCTURE:")
        readme.append("-" * 15)
        for name, df in data_dict.items():
            readme.append(f"{name}:")
            readme.append(f"  Rows: {len(df)}")
            readme.append(f"  Columns: {', '.join(df.columns.tolist())}")
            readme.append("")
        
        readme.append("USAGE INSTRUCTIONS:")
        readme.append("-" * 20)
        readme.append("1. CSV files can be opened in Excel, Google Sheets, or any spreadsheet application")
        readme.append("2. Excel files contain formatted data with styling and multiple sheets")
        readme.append("3. JSON files contain structured data for programmatic access")
        readme.append("4. All numeric values are preserved with original precision")
        readme.append("")
        
        readme.append("For questions or support, please refer to the dashboard documentation.")
        
        return "\n".join(readme)
    
    def create_download_interface(self, data_dict: Dict[str, pd.DataFrame],
                                metadata: Dict[str, Any] = None,
                                export_title: str = "Export Data") -> None:
        """Create a comprehensive download interface."""
        
        st.markdown(f"""
        <div style="background: rgba(26, 30, 46, 0.8); backdrop-filter: blur(10px); 
                    border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 16px; 
                    padding: 2rem; margin: 2rem 0;">
            <h3 style="color: #00d4aa; margin: 0 0 1rem 0; display: flex; align-items: center;">
                <span style="margin-right: 0.5rem;">📥</span>
                {export_title}
            </h3>
            <p style="color: #fafafa; opacity: 0.8; margin: 0 0 2rem 0;">
                Download your analysis results in various formats for further use
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # Export format selection
        col1, col2 = st.columns([1, 2])
        
        with col1:
            st.markdown("**📊 Individual Formats**")
            
            # CSV downloads
            for name, df in data_dict.items():
                csv_data = self.export_csv(df)
                st.download_button(
                    label=f"📄 {name} (CSV)",
                    data=csv_data,
                    file_name=f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv",
                    use_container_width=True
                )
        
        with col2:
            st.markdown("**📈 Comprehensive Formats**")
            
            col2a, col2b = st.columns(2)
            
            with col2a:
                # Excel download
                excel_data = self.export_excel(data_dict)
                st.download_button(
                    label="📊 Excel Workbook",
                    data=excel_data,
                    file_name=f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    use_container_width=True
                )
                
                # JSON download
                if metadata:
                    json_data = self.export_json({
                        'data': {name: df.to_dict('records') for name, df in data_dict.items()},
                        'metadata': metadata
                    })
                    st.download_button(
                        label="🔧 JSON Data",
                        data=json_data,
                        file_name=f"data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json",
                        use_container_width=True
                    )
            
            with col2b:
                # ZIP bundle download
                zip_data = self.export_zip_bundle(data_dict, metadata)
                st.download_button(
                    label="📦 Complete Bundle (ZIP)",
                    data=zip_data,
                    file_name=f"complete_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip",
                    mime="application/zip",
                    use_container_width=True
                )
                
                # Text report
                if metadata:
                    report_data = self.export_pdf_report({
                        'data_overview': {
                            'total_datasets': len(data_dict),
                            'total_records': sum(len(df) for df in data_dict.values())
                        },
                        'metadata': metadata
                    })
                    st.download_button(
                        label="📄 Summary Report",
                        data=report_data,
                        file_name=f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                        mime="text/plain",
                        use_container_width=True
                    )
        
        # Export summary
        st.markdown("---")
        st.markdown("**📋 Export Summary:**")
        
        total_records = sum(len(df) for df in data_dict.items())
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Datasets", len(data_dict))
        with col2:
            st.metric("Total Records", f"{total_records:,}")
        with col3:
            st.metric("Export Formats", "5+")
        with col4:
            st.metric("Export Time", datetime.now().strftime("%H:%M:%S"))

# Global exporter instance
exporter = EnhancedExporter()
"""Enhanced recommendation system with flexible time period analysis."""

import pandas as pd
import numpy as np
import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json

class AdvancedRecommendationSystem:
    """Advanced recommendation system for optimal period analysis with configurable parameters."""
    
    def __init__(self, df: pd.DataFrame):
        self.df = df
        self.analysis_results = {}
    
    def parse_custom_week(self, week_str: str) -> int:
        """Parse custom week format to numeric value for sorting."""
        if pd.isna(week_str):
            return 0
        try:
            year = int(week_str[1:5])
            week = int(week_str[6:8])
            return year * 100 + week
        except (ValueError, IndexError):
            return 0
    
    def format_week_label(self, week_str: str) -> str:
        """Format week string for display."""
        if pd.isna(week_str):
            return ""
        try:
            year = week_str[3:5]
            week = week_str[6:8]
            return f"{year}w{week}"
        except (IndexError, TypeError):
            return str(week_str)
    
    def analyze_optimal_periods(self, selected_weeks: List[str], 
                              analysis_period: int = 12,
                              target_ratio: float = 100.0,
                              countries: List[str] = None,
                              departments: List[str] = None) -> Dict[str, Any]:
        """
        Analyze optimal periods with flexible configuration.
        
        Args:
            selected_weeks: List of weeks to analyze
            analysis_period: Number of consecutive weeks to analyze (1-52)
            target_ratio: Target delivery ratio percentage (default: 100%)
            countries: List of countries to include (None for all)
            departments: List of departments to include (None for all)
        """
        
        if countries is None:
            countries = ['CZ', 'HU', 'SK']
        
        # Filter data
        filtered_df = self.df[self.df['week'].isin(selected_weeks)].copy()
        
        if departments:
            filtered_df = filtered_df[filtered_df['dep name'].isin(departments)]
        
        # Validate analysis period
        if len(selected_weeks) < analysis_period:
            return {
                'error': f'Not enough weeks selected. Need at least {analysis_period} weeks for analysis.',
                'available_weeks': len(selected_weeks),
                'required_weeks': analysis_period
            }
        
        # Group data by department, country, and week
        weekly_data = filtered_df.groupby(['dep name', 'country', 'week'])[
            ['sold_units', 'unit_delivered']
        ].sum().reset_index()
        
        # Calculate delivery ratios
        weekly_data['delivery_ratio'] = (
            weekly_data['unit_delivered'] / weekly_data['sold_units'] * 100
        ).round(2)
        
        # Get unique departments
        departments_to_analyze = sorted(weekly_data['dep name'].unique())
        
        recommendations = []
        analysis_summary = {
            'total_departments': len(departments_to_analyze),
            'analysis_period': analysis_period,
            'target_ratio': target_ratio,
            'weeks_analyzed': len(selected_weeks),
            'countries_included': countries
        }
        
        with st.spinner(f"🔍 Analyzing optimal {analysis_period}-week periods across {len(departments_to_analyze)} departments..."):
            progress_bar = st.progress(0)
            
            for idx, dept in enumerate(departments_to_analyze):
                dept_data = weekly_data[weekly_data['dep name'] == dept].copy()
                
                # Find optimal period for this department
                best_period = self._find_optimal_period(
                    dept_data, selected_weeks, analysis_period, target_ratio, countries
                )
                
                if best_period:
                    recommendations.append({
                        'Department': dept,
                        'Optimal Period Start': self.format_week_label(best_period['start_week']),
                        'Optimal Period End': self.format_week_label(best_period['end_week']),
                        'Period Length': f"{analysis_period} weeks",
                        'Average Ratio CZ': f"{best_period['avg_ratios'].get('CZ', 0):.1f}%",
                        'Average Ratio HU': f"{best_period['avg_ratios'].get('HU', 0):.1f}%",
                        'Average Ratio SK': f"{best_period['avg_ratios'].get('SK', 0):.1f}%",
                        'Overall Average': f"{best_period['overall_avg']:.1f}%",
                        'Distance from Target': f"{best_period['distance_from_target']:.1f}%",
                        'Optimization Score': f"{best_period['score']:.2f}",
                        'Balance Score': f"{best_period['balance_score']:.2f}",
                        'Data Completeness': f"{best_period['completeness']:.1f}%"
                    })
                
                # Update progress
                progress_bar.progress((idx + 1) / len(departments_to_analyze))
            
            progress_bar.empty()
        
        analysis_summary['successful_analyses'] = len(recommendations)
        analysis_summary['failed_analyses'] = len(departments_to_analyze) - len(recommendations)
        
        return {
            'recommendations': recommendations,
            'summary': analysis_summary,
            'raw_data': weekly_data
        }
    
    def _find_optimal_period(self, dept_data: pd.DataFrame, selected_weeks: List[str],
                           analysis_period: int, target_ratio: float, countries: List[str]) -> Optional[Dict]:
        """Find the optimal consecutive period for a specific department."""
        
        if len(selected_weeks) < analysis_period:
            return None
        
        best_period = None
        best_score = float('inf')  # Lower score is better
        
        # Try all possible consecutive periods
        for start_idx in range(len(selected_weeks) - analysis_period + 1):
            end_idx = start_idx + analysis_period - 1
            period_weeks = selected_weeks[start_idx:end_idx + 1]
            
            if len(period_weeks) != analysis_period:
                continue
            
            # Calculate average ratios for this period
            period_ratios = {}
            valid_countries = 0
            total_weeks_with_data = 0
            
            for country in countries:
                country_data = dept_data[
                    (dept_data['country'] == country) &
                    (dept_data['week'].isin(period_weeks))
                ]
                
                if len(country_data) > 0:
                    avg_ratio = country_data['delivery_ratio'].mean()
                    if not pd.isna(avg_ratio) and avg_ratio > 0:
                        period_ratios[country] = avg_ratio
                        valid_countries += 1
                        total_weeks_with_data += len(country_data)
            
            # Require at least some data for analysis
            if valid_countries > 0:
                overall_avg = np.mean(list(period_ratios.values()))
                
                # Calculate distance from target ratio
                distance_from_target = abs(target_ratio - overall_avg)
                
                # Penalty for extreme values
                extreme_penalty = 0
                if overall_avg < 20 or overall_avg > 200:
                    extreme_penalty = 100
                
                # Balance score (lower standard deviation is better)
                if len(period_ratios.values()) > 1:
                    balance_score = np.std(list(period_ratios.values()))
                else:
                    balance_score = 0
                
                # Data completeness score
                max_possible_weeks = analysis_period * len(countries)
                completeness = (total_weeks_with_data / max_possible_weeks) * 100
                completeness_penalty = (100 - completeness) * 0.1
                
                # Final score calculation
                final_score = (
                    distance_from_target + 
                    extreme_penalty + 
                    balance_score * 0.2 + 
                    completeness_penalty
                )
                
                if final_score < best_score:
                    best_score = final_score
                    best_period = {
                        'start_week': period_weeks[0],
                        'end_week': period_weeks[-1],
                        'length': analysis_period,
                        'avg_ratios': period_ratios,
                        'overall_avg': overall_avg,
                        'score': final_score,
                        'distance_from_target': distance_from_target,
                        'balance_score': balance_score,
                        'completeness': completeness,
                        'valid_countries': valid_countries
                    }
        
        return best_period
    
    def create_advanced_filter_interface(self) -> Dict[str, Any]:
        """Create advanced filtering interface for recommendation system."""
        
        st.markdown("""
        <div style="background: rgba(26, 30, 46, 0.8); backdrop-filter: blur(10px); 
                    border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 16px; 
                    padding: 2rem; margin: 2rem 0;">
            <h3 style="color: #00d4aa; margin: 0 0 1rem 0; display: flex; align-items: center;">
                <span style="margin-right: 0.5rem;">🎯</span>
                Advanced Recommendation Filters
            </h3>
            <p style="color: #fafafa; opacity: 0.8; margin: 0 0 2rem 0;">
                Configure your analysis parameters for personalized recommendations
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            analysis_period = st.slider(
                "📊 Analysis Period (weeks)",
                min_value=1,
                max_value=52,
                value=12,
                help="Number of consecutive weeks to analyze for optimal periods"
            )
            
            target_ratio = st.number_input(
                "🎯 Target Delivery Ratio (%)",
                min_value=0.0,
                max_value=200.0,
                value=100.0,
                step=5.0,
                help="Target delivery ratio percentage to optimize towards"
            )
        
        with col2:
            countries = st.multiselect(
                "🌍 Countries to Include",
                options=['CZ', 'HU', 'SK'],
                default=['CZ', 'HU', 'SK'],
                help="Select countries to include in the analysis"
            )
            
            min_data_completeness = st.slider(
                "📈 Minimum Data Completeness (%)",
                min_value=50,
                max_value=100,
                value=80,
                help="Minimum percentage of required data points for valid analysis"
            )
        
        with col3:
            available_depts = sorted(self.df['dep name'].dropna().unique())
            departments = st.multiselect(
                "🏢 Departments to Analyze",
                options=available_depts,
                default=available_depts[:10] if len(available_depts) > 10 else available_depts,
                help="Select specific departments to analyze (leave empty for all)"
            )
            
            sort_by = st.selectbox(
                "📋 Sort Results By",
                options=[
                    "Distance from Target", 
                    "Overall Average", 
                    "Optimization Score",
                    "Balance Score",
                    "Data Completeness"
                ],
                index=0,
                help="Choose how to sort the recommendation results"
            )
        
        return {
            'analysis_period': analysis_period,
            'target_ratio': target_ratio,
            'countries': countries if countries else ['CZ', 'HU', 'SK'],
            'departments': departments if departments else None,
            'min_data_completeness': min_data_completeness,
            'sort_by': sort_by
        }
    
    def display_recommendation_results(self, results: Dict[str, Any], 
                                     sort_by: str = "Distance from Target") -> None:
        """Display recommendation results with enhanced visualization."""
        
        if 'error' in results:
            st.error(f"❌ {results['error']}")
            st.info(f"Available weeks: {results['available_weeks']}, Required: {results['required_weeks']}")
            return
        
        recommendations = results['recommendations']
        summary = results['summary']
        
        if not recommendations:
            st.warning("⚠️ No optimal periods found with the current criteria. Try adjusting the analysis parameters.")
            return
        
        # Display summary
        st.markdown("### 📊 Analysis Summary")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Departments Analyzed", 
                summary['successful_analyses'],
                f"{summary['total_departments']} total"
            )
        
        with col2:
            st.metric(
                "Analysis Period",
                f"{summary['analysis_period']} weeks",
                f"Target: {summary['target_ratio']:.1f}%"
            )
        
        with col3:
            st.metric(
                "Weeks Range",
                summary['weeks_analyzed'],
                f"{len(summary['countries_included'])} countries"
            )
        
        with col4:
            success_rate = (summary['successful_analyses'] / summary['total_departments'] * 100) if summary['total_departments'] > 0 else 0
            st.metric(
                "Success Rate",
                f"{success_rate:.1f}%",
                f"{summary['failed_analyses']} failed"
            )
        
        # Create DataFrame and sort
        recommendations_df = pd.DataFrame(recommendations)
        
        # Sort results
        sort_column_map = {
            "Distance from Target": "Distance from Target",
            "Overall Average": "Overall Average", 
            "Optimization Score": "Optimization Score",
            "Balance Score": "Balance Score",
            "Data Completeness": "Data Completeness"
        }
        
        if sort_by in sort_column_map:
            sort_col = sort_column_map[sort_by]
            if sort_col in recommendations_df.columns:
                # Convert percentage strings to floats for sorting
                numeric_col = recommendations_df[sort_col].str.replace('%', '').astype(float)
                if sort_by == "Distance from Target" or sort_by == "Optimization Score":
                    # Lower is better for these metrics
                    recommendations_df = recommendations_df.iloc[numeric_col.argsort()]
                else:
                    # Higher is better for these metrics
                    recommendations_df = recommendations_df.iloc[numeric_col.argsort()[::-1]]
        
        # Display results table
        st.markdown("### 🎯 Optimization Results")
        
        # Apply styling
        def highlight_ratio_columns(df):
            styles = pd.DataFrame('', index=df.index, columns=df.columns)
            ratio_columns = ['Average Ratio CZ', 'Average Ratio HU', 'Average Ratio SK', 
                           'Overall Average', 'Distance from Target']
            for col in ratio_columns:
                if col in df.columns:
                    styles[col] = 'font-weight: bold; background-color: rgba(0, 212, 170, 0.2); color: #00d4aa;'
            return styles
        
        styled_recommendations = recommendations_df.style.apply(highlight_ratio_columns, axis=None)
        st.dataframe(styled_recommendations, use_container_width=True, height=500)
        
        # Find and highlight best performer
        if len(recommendations) > 0:
            best_dept = recommendations[0]  # Already sorted
            st.success(
                f"🏆 **Best Performance:** {best_dept['Department']} "
                f"({best_dept['Optimal Period Start']} to {best_dept['Optimal Period End']}) "
                f"with {best_dept['Overall Average']} average delivery ratio "
                f"({best_dept['Distance from Target']} from target)"
            )
        
        # Export functionality
        self._create_export_section(recommendations_df, summary)
    
    def _create_export_section(self, df: pd.DataFrame, summary: Dict) -> None:
        """Create export section for recommendation results."""
        
        st.markdown("### 📥 Export Results")
        
        col1, col2, col3 = st.columns(3)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        with col1:
            csv_data = df.to_csv(index=False)
            st.download_button(
                label="📊 Download CSV",
                data=csv_data,
                file_name=f"recommendations_{timestamp}.csv",
                mime="text/csv",
                use_container_width=True
            )
        
        with col2:
            # Create detailed report
            report_data = {
                'analysis_summary': summary,
                'recommendations': df.to_dict('records'),
                'export_timestamp': timestamp
            }
            json_data = json.dumps(report_data, indent=2, default=str)
            st.download_button(
                label="🔧 Download JSON",
                data=json_data,
                file_name=f"recommendations_report_{timestamp}.json",
                mime="application/json",
                use_container_width=True
            )
        
        with col3:
            # Summary report
            summary_text = f"""
Recommendation Analysis Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Analysis Parameters:
- Analysis Period: {summary['analysis_period']} weeks
- Target Ratio: {summary['target_ratio']}%
- Countries: {', '.join(summary['countries_included'])}
- Departments Analyzed: {summary['total_departments']}
- Successful Analyses: {summary['successful_analyses']}

Results Summary:
- Success Rate: {(summary['successful_analyses'] / summary['total_departments'] * 100):.1f}%
- Weeks Analyzed: {summary['weeks_analyzed']}
            """
            st.download_button(
                label="📄 Download Report",
                data=summary_text,
                file_name=f"analysis_summary_{timestamp}.txt",
                mime="text/plain",
                use_container_width=True
            )
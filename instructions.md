# Claude Code Prompt: Transform Streamlit Webapp with Elegant Dark Theme & Enhanced UX

## Project Overview
Transform an existing Streamlit webapp into a modern, elegant dark-themed application with sophisticated animations, improved user experience, and an enhanced recommendation system with flexible time period selection.

## Core Requirements

### 1. Design Transformation
- **Dark Theme Implementation**: Create an elegant dark theme using the latest Streamlit theming capabilities
- **Modern Animations**: Implement smooth CSS animations and transitions throughout the interface
- **Cool Design Elements**: Add modern UI components like glassmorphism effects, subtle gradients, and interactive hover states
- **Professional Aesthetics**: Ensure the design feels premium and polished

### 2. User Experience Enhancement
- **Improved Navigation**: Restructure the app layout for better information hierarchy
- **Interactive Elements**: Add engaging micro-interactions and feedback mechanisms  
- **Responsive Design**: Ensure the interface adapts well to different screen sizes
- **Loading States**: Implement elegant loading animations and progress indicators
- **Error Handling**: Create user-friendly error messages with clear guidance

### 3. Recommendation System Enhancement
- **Flexible Time Selection**: Add a user-configurable option to select analysis periods (1-52 weeks)
- **Advanced Filtering**: Implement multiple filter options for more personalized recommendations
- **Results Visualization**: Create beautiful, interactive charts and visualizations for recommendations
- **Export Functionality**: Allow users to export their recommendations in multiple formats

## Technical Implementation Plan

### Phase 1: Theme & Styling Foundation
1. **Configure Advanced Dark Theme**:
   - Use latest Streamlit 1.49 theming options including:
     - Custom `primaryColor`, `backgroundColor`, `secondaryBackgroundColor`, `textColor`
     - New font configuration options (`baseFontWeight`, `codeFontWeight`)
     - Advanced color customization (`dataframeHeaderBackgroundColor`, `buttonRadius`)
   - Create `.streamlit/config.toml` with sophisticated dark color palette
   - Implement theme consistency across all components

2. **Custom CSS Integration**:
   - Add elegant CSS animations using `st.markdown` with `unsafe_allow_html=True`
   - Implement glassmorphism effects, subtle shadows, and gradient backgrounds
   - Create smooth transitions for interactive elements
   - Add custom scrollbar styling and hover effects

3. **Modern Layout Structure**:
   - Utilize new horizontal flex containers (Streamlit 1.48+ feature) for dynamic layouts
   - Implement proper spacing and visual hierarchy
   - Use the new `width` parameters for buttons, forms, and containers
   - Create responsive grid layouts using `st.columns` with custom widths

### Phase 2: Enhanced User Interface
1. **Interactive Components**:
   - Replace standard inputs with styled alternatives using custom CSS
   - Add animated icons using Material symbols (updated in latest Streamlit)
   - Implement progress bars with smooth animations
   - Create custom loading spinners and status indicators

2. **Navigation & Structure**:
   - Use `st.sidebar` with elegant navigation elements
   - Implement breadcrumb navigation for multi-step processes
   - Add collapsible sections using `st.expander` with custom styling
   - Create tabbed interfaces using `st.tabs` for organized content

3. **Visual Feedback**:
   - Add hover effects on clickable elements
   - Implement smooth state transitions
   - Use `st.toast` for non-intrusive notifications
   - Create animated success/error states

### Phase 3: Recommendation System Upgrade
1. **Time Period Selection Interface**:
   - Create an intuitive week selector using `st.slider` or `st.selectbox`
   - Add preset options (1 week, 1 month, 3 months, 6 months, 1 year)
   - Implement custom date range picker
   - Add validation for time period constraints

2. **Advanced Filtering System**:
   - Multi-select filters for categories, priorities, tags
   - Range sliders for numerical parameters
   - Search functionality with real-time filtering
   - Save/load filter presets

3. **Results Presentation**:
   - Interactive charts using Plotly with dark theme
   - Card-based recommendation display with animations
   - Sortable and filterable results tables
   - Export options (PDF, CSV, JSON) with custom styling

### Phase 4: Advanced Features & Polish
1. **Performance Optimizations**:
   - Use `@st.cache_data` for expensive computations
   - Implement lazy loading for large datasets
   - Optimize chart rendering with proper caching
   - Add loading states during data processing

2. **Accessibility & Usability**:
   - Ensure proper color contrast ratios
   - Add keyboard navigation support
   - Implement clear focus indicators
   - Provide helpful tooltips and guidance

3. **Modern Streamlit Features Integration**:
   - Use `st.query_params` for shareable URLs
   - Implement `st.switch_page` for multi-page navigation
   - Utilize new column configuration options for dataframes
   - Add `st.dialog` for modal interactions

## Latest Streamlit Features to Incorporate

Based on Streamlit 1.49 (August 2025), integrate these cutting-edge features:

- **Horizontal Flex Containers**: For dynamic layouts with configurable alignment and spacing
- **Enhanced Width Parameters**: For precise control over button and component sizing  
- **Advanced Theming Options**: Including separate font weights and refined color controls
- **Improved Dialog System**: With configurable dismissibility and callback functions
- **WebSocket Optimization**: For better real-time performance
- **Material Icons Update**: Latest icon set for modern visual elements

## Code Structure Requirements

1. **Modular Organization**:
   ```python
   # Main app structure
   main.py                 # Entry point
   config/
   ├── theme_config.py     # Theme and styling configuration
   ├── config.toml         # Streamlit configuration
   components/
   ├── ui_components.py    # Custom UI elements
   ├── animations.py       # CSS animations and effects
   ├── charts.py          # Visualization components
   utils/
   ├── recommendation.py   # Enhanced recommendation logic
   ├── data_processing.py  # Data handling utilities
   ├── export_functions.py # Export functionality
   assets/
   ├── styles.css         # Custom CSS styles
   ├── animations.css     # Animation definitions
   ```

2. **Component-Based Architecture**:
   - Create reusable UI components
   - Separate business logic from presentation
   - Use proper error handling and validation
   - Implement consistent styling patterns

## Expected Deliverables

1. **Transformed Application**: Complete dark-themed webapp with modern design
2. **Enhanced Functionality**: Improved recommendation system with flexible time selection
3. **Documentation**: Code comments and setup instructions
4. **Configuration Files**: Properly configured theme and deployment settings
5. **Export Features**: Multiple export options for user convenience

## Success Criteria

- [ ] Professional dark theme implementation with smooth animations
- [ ] Significantly improved user experience and interface design
- [ ] Enhanced recommendation system with configurable time periods
- [ ] Responsive design that works across different devices
- [ ] Fast loading times and smooth interactions
- [ ] Clean, maintainable code structure
- [ ] Integration of latest Streamlit features and best practices

## Additional Considerations

- Ensure backward compatibility with existing data
- Test thoroughly across different browsers and devices
- Implement proper error handling and edge case management
- Follow Streamlit best practices for performance and security
- Consider internationalization if applicable
- Plan for future feature additions and scalability

---

**Note**: This transformation should result in a webapp that feels modern, professional, and engaging while maintaining all existing functionality and adding significant new value through the enhanced recommendation system and superior user experience.
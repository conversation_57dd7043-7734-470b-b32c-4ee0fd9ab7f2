import pandas as pd
import numpy as np
from scipy import stats

# Test the data loading and processing functions
def test_data_loading():
    try:
        df = pd.read_parquet("24w26_25w26.parquet")
        print(f"✅ Data loaded successfully: {df.shape}")
        
        # Test basic operations
        departments = sorted(df['dep name'].dropna().unique().tolist())
        print(f"✅ Found {len(departments)} departments")
        
        countries = df['country'].unique()
        print(f"✅ Found {len(countries)} countries: {list(countries)}")
        
        weeks = sorted(df['week'].unique())
        print(f"✅ Found {len(weeks)} weeks")
        
        return True
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return False

def test_week_parsing():
    try:
        def parse_custom_week(week_str):
            if pd.isna(week_str):
                return 0
            year = int(week_str[1:5])
            week = int(week_str[6:8])
            return year * 100 + week
        
        test_weeks = ['f2024w26', 'f2024w27', 'f2025w01']
        parsed = [parse_custom_week(w) for w in test_weeks]
        print(f"✅ Week parsing works: {test_weeks} -> {parsed}")
        return True
    except Exception as e:
        print(f"❌ Error parsing weeks: {e}")
        return False

def test_number_formatting():
    try:
        def format_large_number(num):
            if pd.isna(num):
                return "0"
            if num >= 1_000_000:
                return f"{num/1_000_000:.1f}M"
            elif num >= 1_000:
                return f"{num/1_000:.1f}K"
            else:
                return f"{num:.0f}"
        
        test_numbers = [123, 1234, 1234567, np.nan]
        formatted = [format_large_number(n) for n in test_numbers]
        print(f"✅ Number formatting works: {test_numbers} -> {formatted}")
        return True
    except Exception as e:
        print(f"❌ Error formatting numbers: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Streamlit App Components")
    print("=" * 40)
    
    all_tests_passed = True
    all_tests_passed &= test_data_loading()
    all_tests_passed &= test_week_parsing()
    all_tests_passed &= test_number_formatting()
    
    print("=" * 40)
    if all_tests_passed:
        print("✅ All tests passed! The app should work correctly.")
    else:
        print("❌ Some tests failed. Please check the issues above.")

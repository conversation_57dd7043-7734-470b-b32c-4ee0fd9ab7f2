import streamlit as st
import streamlit_highcharts as hct

st.title("Tooltip Test")

# Simple test chart with tooltip
config = {
    "title": {"text": "Tooltip Test Chart"},
    "xAxis": {"categories": ["24w26", "24w27", "24w28"]},
    "yAxis": {"title": {"text": "Units"}},
    "tooltip": {
        "shared": True,
        "crosshairs": True,
        "pointFormat": "<span style='color:{series.color}'>{series.name}</span>: <b>{point.y:,.0f}</b><br/>"
    },
    "series": [
        {
            "type": "column",
            "name": "Sold Units",
            "data": [1000, 1200, 1100],
            "color": "#4e79a7"
        },
        {
            "type": "column",
            "name": "Delivered Units", 
            "data": [950, 1150, 1050],
            "color": "#a0cbe8"
        }
    ]
}

hct.streamlit_highcharts(config, height=400)

st.write("Hover over the bars to see if tooltip appears with sold/delivered units values.")
